{"name": "payroll-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "axios": "^1.7.7", "file-saver": "^2.0.5", "formik": "^2.4.6", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.474.0", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^6.27.0", "react-toastify": "^11.0.3", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "vite": "^5.4.10"}}