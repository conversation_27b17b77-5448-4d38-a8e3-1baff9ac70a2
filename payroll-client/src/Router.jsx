/* eslint-disable react/prop-types */

import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Login from "./routes/Login";
import Home from "./routes/Home";
import Client from "./routes/Client";
import Employee from "./routes/Employee";
import Salary from "./routes/Salary";
import Attendance from "./routes/Attendance";
import Payment from "./routes/Payment";
import PayrollManagement from "./routes/PayrollManagement/Index";
import { ToastContainer } from 'react-toastify';
import EPFDetails from "./routes/Epf-details/EpfDetails";
const Router = ({ isAuthenticated }) => {
  if (isAuthenticated) {
    return (
      <BrowserRouter>
        <ToastContainer />

        <Routes>
          <Route path="/" element={<Home />} />
          <Route
            path="/:companyId/clients/:clientId/employees/*"
            element={<Employee />}
          />
          <Route
            path="/:companyId/clients/:clientId/attendance/*"
            element={<Attendance />}
          />
          <Route
            path="/:companyId/clients/:clientId/salaries/*"
            element={<Salary />}
          />
          <Route
            path="/:companyId/clients/:clientId/payments/*"
            element={<Payment />}
          />
           <Route
            path="/:companyId/payroll/*"
            element={<PayrollManagement />}
          />
         
          
          <Route path="/:companyId/clients/*" element={<Client />} />
          
        </Routes>
      </BrowserRouter>
    );
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="*" element={<Navigate to="/login" replace={true} />} />
      </Routes>
    </BrowserRouter>
  );
};

export default Router;
