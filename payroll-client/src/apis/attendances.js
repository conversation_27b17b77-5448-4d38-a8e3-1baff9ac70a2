import axiosInstance from "../configs/axios";

const BASE_URL = "/attendances";

const fetchAll = (filters) =>
  axiosInstance.get(BASE_URL, { params: { ...filters } });

const fetchCompany= (filters) =>
  axiosInstance.get(`${BASE_URL}/company`, { params: { ...filters } });

const createOne = (payload) => axiosInstance.post(BASE_URL, payload);

const updateOne = (id, payload) =>
  axiosInstance.put(`${BASE_URL}/${id}`, payload);

const attendancesApi = { fetchAll, createOne, updateOne,fetchCompany };
export default attendancesApi;
