import axiosInstance from "../configs/axios";

const BASE_URL = "/employees";

const fetchAll = (client_id, status = undefined) =>
  axiosInstance.get(BASE_URL, {
    params: { client_id, status: status || "ACTIVE" },
  });

const fetchOne = (id) => axiosInstance.get(`${BASE_URL}/${id}`);

const createOne = (payload) => axiosInstance.post(BASE_URL, payload);

const updateOne = (id, payload) =>
  axiosInstance.put(`${BASE_URL}/${id}`, payload);

const updateStatus = (id, status) =>
  axiosInstance.put(`${BASE_URL}/${id}/status`, { status });

const employeesApi = { fetchAll, fetchOne, createOne, updateOne, updateStatus };
export default employeesApi;
