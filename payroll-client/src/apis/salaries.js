import axiosInstance from "../configs/axios";

const BASE_URL = "/salaries";

const fetchAll = (filters) =>
  axiosInstance.get(BASE_URL, { params: { ...filters } });

const companyFetchAll = (filters) =>
  axiosInstance.get(`${BASE_URL}/company`, { params: { ...filters } });

const fetchOne = (id) => axiosInstance.get(`${BASE_URL}/${id}`);
const fetchOneWageSlip = (id) => axiosInstance.get(`${BASE_URL}/wageslip/${id}`);
const fetchCompanyDataByPeriod = (id,period) => axiosInstance.get(`${BASE_URL}/${id}/${period}`);


const fetchInvoice = (params) =>
  axiosInstance.get(`${BASE_URL}/invoice`, { params });

const addAdvance = (payload) =>
  axiosInstance.post(`${BASE_URL}/advance`, payload);

const generate = (payload) =>
  axiosInstance.post(`${BASE_URL}/generate`, payload);

const getSalaryData = ({period,companyId}) =>
  axiosInstance.get(`${BASE_URL}/data/${companyId}/${period}`);

const updateSalaryData = (id,updatedItem) =>
  axiosInstance.patch(`${BASE_URL}/salaries/${id}`,{...updatedItem});


const updateEpfData = (id,updatedItem) =>
  axiosInstance.patch(`${BASE_URL}/epf/${id}`,{...updatedItem});

const salariesApi = { fetchAll, fetchOne, fetchInvoice, addAdvance, generate ,getSalaryData,updateSalaryData,updateEpfData,companyFetchAll,fetchCompanyDataByPeriod,fetchOneWageSlip};
export default salariesApi;
