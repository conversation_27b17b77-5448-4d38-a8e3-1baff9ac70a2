import { useParams } from "react-router-dom";
import Sidebar from "./Sidebar";

const ClientManagementSidebar = () => {
  const { companyId, clientId } = useParams();

  const MAIN_LINKS = [
    {
      label: "All Employees",
      route: `/${companyId}/clients/${clientId}/employees`,
    },
    {
      label: "New Employee",
      route: `/${companyId}/clients/${clientId}/employees/new`,
    },
    {
      label: "Attendance",
      route: `/${companyId}/clients/${clientId}/attendance`,
    },
    {
      label: "Register of wages",
      route: `/${companyId}/clients/${clientId}/salaries`,
    },
    {
      label: "Payments",
      route: `/${companyId}/clients/${clientId}/payments`,
    },
   
  ];

  const BOTTOM_LINKS = [
    { label: "Back to Clients", route: `/${companyId}/clients` },
  ];

  return <Sidebar mainLinks={MAIN_LINKS} bottomLinks={BOTTOM_LINKS} />;
};

export default ClientManagementSidebar;
