import React from 'react'
import { useNavigate } from 'react-router-dom'

function PaymentButton() {
  const navigate = useNavigate()  // Hook for navigation
  
  // Function to handle the button click and navigate
  const openPayment = () => {
    navigate('/payment')  // This will navigate to the '/payment' route
  }

  return (
    <div>
      <button onClick={openPayment}>Pay</button>
    </div>
  )
}

export default PaymentButton
