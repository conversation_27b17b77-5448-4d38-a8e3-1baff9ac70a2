import { useEffect, useState } from "react";
import { useNavigate, usePara<PERSON>, Link } from "react-router-dom";
import { 
  Home, 
  ChevronRight, 
  ChevronDown, 
  Settings, 
  BarChart, 
  Users, 
  CreditCard, 
  LogOut 
} from 'lucide-react';
import companiesApi from "../apis/companies";

const Sidebar = ({ mainLinks, bottomLinks }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [company, setCompany] = useState();
  const [expandedSections, setExpandedSections] = useState({});

  const { companyId } = useParams();
  const navigate = useNavigate();

  const getCompanyDetails = async () => {
    setIsLoading(true);
    const storedDetails = JSON.parse(
      localStorage.getItem("selected-company") || "{}"
    );

    if (storedDetails.id === companyId) {
      setCompany(storedDetails);
    } else {
      try {
        const { data } = await companiesApi.fetchOne(companyId);

        localStorage.setItem(
          "selected-company",
          JSON.stringify(data.company)
        );
        localStorage.setItem(
          "selected-company-logo-url",
          JSON.stringify(data.logo_url)
        );

        setCompany(data.company);
      } catch (err) {
        navigate("/");
        console.error(err);
      }
    }
    setIsLoading(false);
  };

  useEffect(() => {
    getCompanyDetails();
    // Update active route when location changes
    
  }, [companyId, navigate]);

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const renderIcon = (label) => {
    const iconMap = {
      'Dashboard': <Home className="mr-2 w-4 h-4" />,
      'Analytics': <BarChart className="mr-2 w-4 h-4" />,
      'Users': <Users className="mr-2 w-4 h-4" />,
      'Billing': <CreditCard className="mr-2 w-4 h-4" />,
      'Settings': <Settings className="mr-2 w-4 h-4" />
    };
    return iconMap[label] || <ChevronRight className="mr-2 w-4 h-4" />;
  };

  if (isLoading) {
    return (
      <aside className="flex-none min-w-56 flex justify-center items-center">
        <div className="w-14 h-14 border-8 border-green-500 border-b-transparent rounded-full animate-spin" />
      </aside>
    );
  }

  return (
    <aside className="flex-none w-60  flex flex-col border-r bg-white">
      <div className="flex items-center justify-between px-5 py-3 border-b">
        <div className="flex items-center">
          <img
            src={company.logo_url}
            className="h-28 w-20 mr-3 rounded"
            alt={`${company.name} logo`}
          />
          <h3 className="font-semibold text-sm">{company.name}</h3>
        </div>
      </div>
      
      
      
      {mainLinks && (
        <div className="flex-1 flex flex-col gap-1 p-3 overflow-y-auto">
          {mainLinks.map((item, idx) => (
            item.subLinks ? (
              <div key={`action-${idx}`} className="mb-1">
                <button
                  onClick={() => toggleSection(item.label)}
                  className={`
                    w-full flex items-center justify-between 
                    px-3 py-2 rounded 
                    ${expandedSections[item.label] ? 'bg-green-100' : 'hover:bg-green-50'}
                  `}
                >
                  <div className="flex items-center">
                    {renderIcon(item.label)}
                    {item.label}
                  </div>
                  {expandedSections[item.label] ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </button>
                
                {expandedSections[item.label] && (
                  <div className="ml-4 mt-1 flex flex-col">
                    {item.subLinks.map((subItem, subIdx) => (
                      <Link
                        key={`sub-action-${subIdx}`}
                        to={subItem.route}
                        className={`
                          flex items-center 
                          px-3 py-2 rounded 
                          
                             'hover:bg-green-50'
                        `}
                      >
                        {renderIcon(subItem.label)}
                        {subItem.label}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <Link
                key={`action-${idx}`}
                to={item.route}
                className={`
                  flex items-center 
                  px-3 py-2 rounded 
                  
                 'hover:bg-green-50'}
                `}
              >
                {renderIcon(item.label)}
                {item.label}
              </Link>
            )
          ))}
        </div>
      )}
      
        <div className="flex flex-col border-t p-3"
        >
        <Link
        to="/"
        className="flex items-center justify-between px-5 py-2.5 text-sm bg-gray-100 hover:bg-gray-200 border-t border-b"
      >
        <span className="flex items-center">
          <LogOut className="mr-2 w-4 h-4" /> Switch company
        </span>
        <ChevronRight className="w-4 h-4" />
      </Link>
      {bottomLinks && (

          bottomLinks.map((item, idx) => (
            <Link
              key={`action-${idx}`}
              to={item.route}
              className={`
                flex items-center 
                px-3 py-2 rounded 
                
                  hover:bg-gray-100 
              `}
            >
              {renderIcon(item.label)}
              {item.label}
            </Link>
    ))

      )}
        </div>
    </aside>
  );
};

export default Sidebar;