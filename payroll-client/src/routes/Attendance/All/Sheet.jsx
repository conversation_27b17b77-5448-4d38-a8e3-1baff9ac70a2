/* eslint-disable react/prop-types */

import { useMemo , useCallback, useRef, useEffect } from "react";
import { getCurrentYearMonthDay, getDates } from "../../../utils/datetime";
import debounce from "lodash.debounce";
import jsPDF from "jspdf";
import "jspdf-autotable";
import * as XLSX from "xlsx";
const ActionButton = ({ label, onClick }) => (
  <button
    className="px-2 py-0.5 bg-transparent text-green-500 rounded border border-green-500 hover:bg-green-500 hover:text-white duration-150"
    onClick={onClick}
  >
    {label}
  </button>
);

const Sheet = ({
  company,
  period,
  dataset,
  onTogglePresence,
  onSetAdvance,
  onGenerateSalary,
  
}) => {
  const allDates = useMemo(
    () =>
      getDates(
        Math.trunc(period / 100),
        period - Math.trunc(period / 100) * 100
      ),
    [period]
  );

const calculateTotalPresentDays = (attendances) => {
  return Object.values(attendances).reduce((total, attendance) => {
    return total + (attendance.presence > 0 ? 1 : 0);
  }, 0);
};
  const getValue = (datasetRow, dateItem) => {
    if (dateItem.date in datasetRow.attendances) {
      if (datasetRow.attendances[dateItem.date].presence) return "✔";
      return "✘";
    }
    return dateItem.isSunday ? "?" : "✔";
  };

  const onAdvanceChange = useCallback(
    debounce((e, row, onSetAdvance) => {
      console.log(Number(e.target.value));

      if (Number(e.target.value) === row.advance) return;
      onSetAdvance(row.employee.id, e.target.value);
    }, 1500),
    []
  );
  

  const downloadAttendancePDF = (dataset, allDates, period) => {
    const doc = new jsPDF({
      orientation: "landscape",
      unit: "mm",
      format: "a4",
    });
  

    const pageWidth = doc.internal.pageSize.width;
    const clientData = JSON.parse(localStorage.getItem("selected-client") || "{}");
    const companyData = JSON.parse(localStorage.getItem("selected-company") || "{}");

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("FORM XVI", pageWidth / 2, 15, { align: "center" });
    
    doc.setFontSize(10);
    doc.text("See Rule 78(a)(i)", pageWidth / 2, 22, { align: "center" });
  
    doc.setFontSize(14);
    doc.text("MUSTER ROLL", pageWidth / 2, 30, { align: "center" });
  
 
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
  
    const employerDetails = [
      ["Name and Address of Contractor:", `${companyData?.name}, ${companyData?.address_1}`],
      ["Nature of work:", `${clientData?.contract_category}`],
      // ["Name and address of establishment:", "INCOMETAX OFFICE, VIDYANAGAR, Kasargod"],
      ["Principal Employer:", `${clientData.name}`],
      ["Period:", `${Math.trunc(period / 100)}-${String(period % 100).padStart(2, "0")}`],
    ];
  
    let yPos = 40;
  
    employerDetails.forEach(([label, value]) => {
      doc.setFont("helvetica", "bold");
      doc.text(label, 15, yPos);
      doc.setFont("helvetica", "normal");
      doc.text(value, 80, yPos);
      yPos += 6; // Line spacing
    });
  

    const headers = [
      ["EmpID", "Name", ...allDates.map((item) => String(item.date % 100).padStart(2, "0"))],
    ];
  

    const data = dataset.map((row) => [
      row.employee.employee_id,
      row.employee.name,
      ...allDates.map((item) =>
        item.date in row.attendances
          ? row.attendances[item.date].presence > 0
            ? "P"
            : "A"
          : ""
      ),
    ]);
   
   
    doc.autoTable({
      head: headers,
      body: data,
      startY: yPos + 8,
      styles: { fontSize: 7, cellPadding: 1 },
      theme: "grid",
      columnStyles: { 0: { cellWidth: 15 }, 1: { cellWidth: 40 } }, 
      headStyles: {
        fillColor: [220, 220, 220],
        textColor: [0, 0, 0],
        fontStyle: "bold",
      },
    });
  

    doc.save("Muster_Roll.pdf");
  };
  

 
  const handleExportAttendance = () => {
    const sheetData = dataset.map((item, i) => {
      const row = {
        "S. No": i + 1,
        "EmpID": item.employee.employee_id,
        "Name": item.employee.name,
      };
  
      allDates.forEach((date) => {
        row[date.date % 100] = item.attendances[date.date]?.presence > 0 ? "P" : "A";
      });
  
      return row;
    });
  
    const ws = XLSX.utils.json_to_sheet(sheetData);
  
    const headers = ["S. No", "EmpID", "Name", ...allDates.map((date) => String(date.date % 100))];
    ws["!ref"] = XLSX.utils.encode_range({
      s: { r: 0, c: 0 }, // start cell (A1)
      e: { r: sheetData.length, c: headers.length - 1 },
    });
  
    XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
  
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `Attendance_${period}`);
  
    XLSX.writeFile(wb, `Attendance_${period}.xlsx`);
  };
  
  useEffect(() => {
    dataset.forEach(row => {
      // Skip if row is locked
      if (row.isLocked) return;
      const advanceValue = Number(row.advance);
      // Handle string contributions by removing quotes if present
      const contributionValue = Number(row.employee?.contribution?.replace?.(/"/g, '') || 0);
      
      if ((advanceValue === 0 || row.advance === "0") && contributionValue > 0) {
        onSetAdvance(row.employee.id, contributionValue);
      }
    });
  }, [dataset, onSetAdvance]);

  return (
    <>
    <div className="flex justify-end mb-2">
   {
    ! company &&(
      <>
       <button
        onClick={() => downloadAttendancePDF(dataset, allDates, period)}
        className="px-4 py-2 text-black bg-white border border-black rounded ml-1  text-sm font-medium"
      >
        Download PDF
      </button> 

      <button
        onClick={() => handleExportAttendance()}
        className="px-4 py-2 text-black bg-white border border-black rounded ml-1  text-sm font-medium"
      >
        Export Excel
      </button> 
      </>
    )
   }

    </div>
   
    <table className="bg-white w-full overflow-hidden text-xs">
  <thead className="border-b bg-gray-100 text-left">
    <tr>
      <th className="px-1 py-0.5 w-32 font-medium">EmpID</th>
      <th className="px-1 py-0.5 min-w-40 font-medium">Name</th>
      {allDates.map((item) => (
        <th key={item.date} className="px-1 py-0.5 border font-medium text-center">
          <div>{String(item.date % 100).padStart(2, "0")}</div>
          <div className="text-xs font-light">
            {new Date(Math.trunc(period / 100), (period % 100) - 1, item.date % 100)
              .toLocaleDateString("en-US", { weekday: "short" })
              .substring(0, 2)}
          </div>
        </th>
      ))}
      <th className="sticky left-0 z-10 bg-white px-4 py-2 text-sm font-semibold text-gray-900 text-left border-b">
      Total Present
    </th>
      <th className="px-1 py-0.5 border font-medium">Advance</th>
      <th className="px-1 py-0.5">{/* ACTIONS */}</th>
    </tr>
  </thead>

  <tbody className="font-thin">
    {dataset.map((row) => (
      <tr key={row.employee.id}>
        <td className={`px-1 py-0.5 border ${row.employee.status === "INACTIVE" ? " bg-gray-100" : ""}`}>
          {row.employee.employee_id}
        </td>
        <td className={`px-1 py-0.5 border ${row.employee.status === "INACTIVE" ? " bg-gray-100" : ""}`}>
          {row.employee.name}
        </td>
        {allDates.map((item) => {
          if (item.date >= getCurrentYearMonthDay()) {
            return (
              <td className="px-1 py-0.5 border bg-gray-300 cursor-not-allowed" key={`${row.employee.id}-${item.date}`}></td>
            );
          }
          
          const existingAttendance = item.date in row.attendances;
          const isPresent = existingAttendance ? 
            row.attendances[item.date].presence > 0 : 
            !item.isSunday;

          return (
            <td
              className={`text-center px-1 py-0.5 border ${
                isPresent ? "bg-green-300" : existingAttendance ? "bg-red-300" : ""
              } ${row.isLocked ? "" : "cursor-pointer"}`}
              key={`${row.employee.id}-${item.date}`}
              onClick={() => onTogglePresence(row.employee.id, item.date)}
            >
              {getValue(row, item)}
            </td>
          );
        })}
         <td className="px-4 py-2 text-sm text-gray-900 border-b">
        {calculateTotalPresentDays(row.attendances)}
      </td>


        <td className="px-1 py-0.5 border">
          <input
            defaultValue={row.advance}
            onChange={(e) => onAdvanceChange(e, row, onSetAdvance)}
            disabled={row.isLocked}
            className="px-1 py-0.5 text-xs border rounded disabled:bg-gray-100 disabled:cursor-not-allowed w-16"
          />
        </td>
        <td className="px-1 py-0.5 border">
          {
            !company && (
              <ActionButton label="Generate" onClick={() => onGenerateSalary(row.employee.id)} />

            )
          }
        </td>
      </tr>
    ))}
  </tbody>
</table>


    </>
    
  );
};

export default Sheet;
