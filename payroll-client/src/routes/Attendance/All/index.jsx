import { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import attendancesApi from "../../../apis/attendances";
import salariesApi from "../../../apis/salaries";
import PageLoader from "../../../components/PageLoader";
import Error from "../../../components/Error";
import { getCurrentYearMonth, getDates } from "../../../utils/datetime";
import { getSheetData } from "../../../utils/attendance";
import DateFilter from "./DateFilter";
import Sheet from "./Sheet";
import { toast } from "react-toastify";
import jsPDF from "jspdf";
import * as XLSX from "xlsx";

const All = ({company}) => {


  const [isLoading, setIsLoading] = useState(true);
  const [period, setPeriod] = useState(getCurrentYearMonth());
  const [attendances, setAttendances] = useState([]);
  const [isEditable, setIsEditable] = useState(true);
  const [alertMessage, setAlertMessage] = useState("");
 
  const containerRef = useRef(null);
  const scrollPositionRef = useRef(null);
 
  const { clientId ,companyId} = useParams();
 
  const companydata = JSON.parse(localStorage.getItem("selected-company"));
  const clientData = JSON.parse(localStorage.getItem("selected-client"));

  // Add this helper function to check if a date is Sunday
  const isSunday = (dateString) => {
    const date = new Date(
      parseInt(dateString.toString().slice(0, 4)),
      parseInt(dateString.toString().slice(4, 6)) - 1,
      parseInt(dateString.toString().slice(6, 8))
    );
    return date.getDay() === 0; // 0 represents Sunday
  };

  // Add this new function to handle initial attendance marking
  const markInitialAttendance = async (attendanceData) => {
    const year = Math.trunc(period / 100);
    const month = period - year * 100;
    const datesInMonth = getDates(year, month);
  
    try {
      for (const employeeData of attendanceData) {
        // Get all dates that need attendance records
        const datesNeedingAttendance = datesInMonth.filter(dateObj => {
          const date = dateObj.date;
          // Only include dates that:
          // 1. Don't already have attendance
          // 2. Aren't Sundays
          return !employeeData.attendances[date] && !isSunday(date);
        });
  
        if (datesNeedingAttendance.length > 0) {
          // Create payloads for all dates needing attendance
          const payloads = datesNeedingAttendance.map(dateObj => ({
            client_id: clientId,
            employee_id: employeeData.employee.id,
            presence: 1,
            date: dateObj.date,
          }));
  
          // Use Promise.all to send all requests in parallel
          // You might want to implement this in your API as a batch operation
          await Promise.all(
            payloads.map(payload => attendancesApi.createOne(payload))
          ).catch(error => {
            // Ignore duplicate key errors, but log other errors
            if (!error.response?.data?.includes('unique_attendance_combination')) {
              console.error('Error creating attendance:', error);
            }
          });
        }
      }
    } catch (err) {
      console.error("Error marking initial attendance:", err);
    }
  };

  const fetchData = async () => {
    setIsLoading(true);
    try {
      if(!companyId) return;

      let data = {};
      if(company){
        const res = await attendancesApi.fetchCompany({ companyId, period });
        data = res.data;
        const sheetData = getSheetData(data.attendances, true);
        await markInitialAttendance(sheetData); // Add this line
        setAttendances(sheetData);
      } else {
        const res = await attendancesApi.fetchAll({ clientId, period });
        data = res.data;
        const sheetData = getSheetData(data.attendances, false);
        await markInitialAttendance(sheetData); // Add this line
        setAttendances(sheetData);
      }
      
      setIsEditable(period === getCurrentYearMonth());
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
 

 
 
  const onTogglePresence = async (employeeId, date) => {
 
    const datasetRecord = attendances.find(
      (item) => item.employee.id === employeeId
    );
 
    const dateItem = datasetRecord.attendances[date];
    try {
      const payload = {
        client_id: clientId,
        employee_id: employeeId,
        presence: dateItem && dateItem.presence ? 0 : 1,
        date,
      };
      if (dateItem) {
        await attendancesApi.updateOne(dateItem.id, payload);
      } else {
        await attendancesApi.createOne(payload);
      }
 
      if (containerRef.current) {
        scrollPositionRef.current = containerRef.current.scrollLeft;
      }
      await fetchData();
    } catch (err) {
      console.error(err);
    }
  };
 
  const onSetAdvance = async (employeeId, advance) => {
   
 
    try {
      const payload = {
        advance: Number(advance),
        employee_id: employeeId,
        period,
      };
      await salariesApi.addAdvance(payload);
      if (containerRef.current) {
        scrollPositionRef.current = containerRef.current.scrollLeft;
      }
      await fetchData();
    } catch (err) {
      console.error(err);
    }
  };
 
  const onGenerateSalary = async (employeeId) => {

 
    try {
      const payload = { employee_id: employeeId, period };
      await salariesApi.generate(payload);
 
     
    } catch (err) {
      
      if(err?.response?.data?.error){
        toast.error(err?.response?.data?.error, {
          position: "top-right",
          autoClose: 4000,    
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "colored",
        });
      }

      
      console.error(err);
    }
  };
 
 
  const markAllPresent = async (clientId) => {
    const year = Math.trunc(period / 100);
    const month = period - year * 100;
    const datesInMonth = getDates(year, month);
   
    setIsLoading(true);
    try {
 
      for (const row of attendances) {
        if (row.employee.id === clientId) {
          for (const d of datesInMonth) {
     
            const dateItem = row.attendances[d.date];
     
            if (!dateItem ) {
              const payload = {
                client_id: clientId,
                employee_id: row.employee.id,
                presence: 1,
                date: d.date,
              };
              if (dateItem) {
                await attendancesApi.updateOne(dateItem.id, payload);
              } else {
                await attendancesApi.createOne(payload);
              }
            }
          }
        }
      }
 
      if (containerRef.current) {
        scrollPositionRef.current = containerRef.current.scrollLeft;
      }
 
      await fetchData();
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  // -------------------------------------------
 
  useEffect(() => {
    if (companyId) {
      fetchData();
      
    }
  }, [companyId, period]);
 
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollLeft = scrollPositionRef.current;
    }
  });
 
  const handleExportAllExcel = () => {
    const wb = XLSX.utils.book_new();
    
    attendances.forEach((clientGroup) => {
      const allDates = getDates(
        Math.trunc(period / 100),
        period - Math.trunc(period / 100) * 100
      );

      const sheetData = clientGroup.employees.map((item, i) => {
        const row = {
          "S. No": i + 1,
          "Client": clientGroup.client.name,
          "EmpID": item.employee.employee_id,
          "Employee Name": item.employee.name,
          "Status": item.employee.status || 'ACTIVE',
        };
    
        allDates.forEach((date) => {
          row[date.date % 100] = item.attendances[date.date]?.presence > 0 ? "P" : "A";
        });
    
        return row;
      });

      // Truncate sheet name to 31 characters and remove invalid characters
      let sheetName = `${period}_${clientGroup.client.name}`
        .replace(/[\\/?*[\]]/g, '') // Remove invalid characters
        .trim()
        .substring(0, 31); // Limit to 31 chars
      
      // Ensure unique sheet name if truncated names are the same
      let counter = 1;
      let originalSheetName = sheetName;
      while (wb.SheetNames.includes(sheetName)) {
        const suffix = ` (${counter})`;
        sheetName = originalSheetName.substring(0, 31 - suffix.length) + suffix;
        counter++;
      }

      const ws = XLSX.utils.json_to_sheet(sheetData);

      // Set column widths
      const colWidths = {
        A: { wch: 8 },     // S. No
        B: { wch: 30 },    // Client
        C: { wch: 12 },    // EmpID
        D: { wch: 30 },    // Employee Name
        E: { wch: 10 },    // Status
      };
      ws['!cols'] = Object.values(colWidths);

      XLSX.utils.book_append_sheet(wb, ws, sheetName);
    });

    XLSX.writeFile(wb, `All_Attendance_Reports_${period}.xlsx`);
  };

  const downloadAllPDF = () => {
    const doc = new jsPDF({
      orientation: "landscape",
      unit: "mm",
      format: "a4",
    });

    attendances.forEach((clientGroup, index) => {
      if (index > 0) {
        doc.addPage();
      }

      const pageWidth = doc.internal.pageSize.width;
      const leftMargin = 10;
      let yPos = 15;

      // Header section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(16);
      doc.text("FORM XVI", pageWidth / 2, yPos, { align: "center" });
      yPos += 7;

      doc.setFontSize(11);
      doc.text("See Rule 78(a)(i)", pageWidth / 2, yPos, { align: "center" });
      yPos += 7;

      doc.text("Muster Roll", pageWidth / 2, yPos, { align: "center" });
      yPos += 10;

      // Improved header details handling
      doc.setFontSize(10);
      const labelWidth = 85;
      const valueWidth = pageWidth - leftMargin - labelWidth - 25;

      const headerDetails = [
        {
          label: "Name and Address of Contractor:",
          value: `${companydata?.name}, ${companydata?.address_1}`
        },
        {
          label: "Nature and location of work:",
          value: `${clientGroup.client.contract_category}, ${clientGroup.client.address}`
        },
        {
          label: "Name and address of establishment in/under which contract is carried on:",
          value: "ASSISTANT COMMISSIONER OF INCOME TAX, SHASTHRI ROAD, PUBLIC LIBRARY BUILDING, KOTTAYAM, KERALA-686001"
        },
        {
          label: "Name and address of Principal Employer:",
          value: "INCOME TAX OFFICE, KOTTAYAM"
        },
        {
          label: "Period:",
          value: `${period.toString().slice(0, 4)} ${period.toString().slice(4)}`
        }
      ];

      headerDetails.forEach(({ label, value }) => {
        doc.setFont("helvetica", "bold");
        const splitLabel = doc.splitTextToSize(label, labelWidth);
        doc.text(splitLabel, leftMargin, yPos);

        doc.setFont("helvetica", "normal");
        const splitValue = doc.splitTextToSize(value || '', valueWidth);
        doc.text(splitValue, leftMargin + labelWidth, yPos);

        const labelLines = splitLabel.length;
        const valueLines = splitValue.length;
        const lineHeight = 5;
        const maxLines = Math.max(labelLines, valueLines);
        yPos += maxLines * lineHeight + 2;
      });

      // Get dates for the period
      const allDates = getDates(
        Math.trunc(period / 100),
        period - Math.trunc(period / 100) * 100
      );

      // Create table headers
      const headers = [
        ["EmpID", "Name", ...allDates.map((item) => String(item.date % 100).padStart(2, "0"))]
      ];

      // Create table data with colored attendance markers
      const data = clientGroup.employees.map((row) => [
        row.employee.employee_id,
        row.employee.name,
        ...allDates.map((item) =>
          item.date in row.attendances
            ? row.attendances[item.date].presence > 0
              ? { content: "P", styles: { textColor: [0, 128, 0] } } // Green color for Present
              : { content: "A", styles: { textColor: [255, 0, 0] } }  // Red color for Absent
            : ""
        )
      ]);

      // Add table to PDF
      doc.autoTable({
        head: headers,
        body: data,
        startY: yPos + 5,
        styles: {
          fontSize: 8,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [240, 240, 240],
          textColor: [0, 0, 0],
          fontStyle: 'bold',
          halign: 'center',
          valign: 'middle'
        },
        columnStyles: {
          0: { cellWidth: 15 },
          1: { cellWidth: 40 }
        },
        theme: 'grid',
        tableWidth: 'auto',
        margin: { left: leftMargin }
      });
    });

    doc.save(`All_Attendance_Reports_${period}.pdf`);
  };

  if (isLoading) {
    return <PageLoader />;
  }
 
  if (!attendances) {
    return <Error title="Could not load attendances" />;
  }
 
  return (
    <>
      <header className="w-full flex justify-between items-center gap-5 mb-5">
        <h2 className="text-2xl leading-none font-semibold">
          Attendance Details
        </h2>
        <div className=" items-center gap-2">
          <DateFilter period={period} setPeriod={setPeriod} />
          {company && (
            <div className="mt-5">
              <button
                onClick={downloadAllPDF}
                className="px-4 py-2 text-black bg-white border border-black rounded ml-1 text-sm font-medium"
              >
                Download All PDF
              </button>
              <button
                onClick={handleExportAllExcel}
                className="px-4 py-2 text-black bg-white border border-black rounded ml-1 text-sm font-medium"
              >
                Export All
              </button>
            </div>
          )}
        </div>
      </header>
 
      {company ? (
        <div className="space-y-8">
          {attendances.map((clientGroup) => (
            <section
              key={clientGroup.client.id}
              ref={containerRef}
              className="w-full overflow-x-auto pb-1 scrollbar rounded"
            >
              <div className="p-4 rounded-md text-black font-bold">
                <h3 className="text-lg font-bold text-center">FORM XVI</h3>
                <p className="text-sm font-bold text-center">See Rule 78(a)(i)</p>
                <p className="text-sm font-bold text-center">Muster Roll</p>
                <p className="text-sm">
                  Name and Address of Contractor :
                  <span className="font-thin text-sm">
                    {`${companydata?.name}, ${companydata?.address_1}`}
                  </span>
                </p>
                <p className="text-sm">
                  Nature and location of work :
                  <span className="font-thin text-sm">
                    {`${clientGroup.client.contract_category}`}
                  </span>
                </p>
                <p className="text-sm">
                  Name and address of establishment in/under which contract is carried on :
                  <span className="font-thin text-sm">
                    {clientGroup.client.address}
                  </span>
                </p>
                <p className="text-sm">
                  Name and address of Principal Employer :
                  <span className="font-thin text-sm">
                    {`${clientGroup.client.name}, ${clientGroup.client.address}`}
                  </span>
                </p>
                {period && (
                  <p className="text-sm">
                    Period: {`${period.toString().slice(0,4)} ${period.toString().slice(4)}`}
                  </p>
                )}
                {/* <div className="h-full w-full flex justify-end">
                  <button
                    onClick={() => markAllPresent(clientGroup.client.id)}
                    className="px-4 py-2 text-black bg-white border border-black rounded ml-1 text-sm font-medium"
                  >
                    Mark All Present
                  </button>
                </div> */}
              </div>

              <Sheet
                period={period}
                dataset={clientGroup.employees}
                onTogglePresence={onTogglePresence}
                onSetAdvance={onSetAdvance}
                onGenerateSalary={onGenerateSalary}
                company={company} 

              />
            </section>
          ))}
        </div>
      ) : (
        // Original single client view
        <section
          ref={containerRef}
          className="w-full overflow-x-auto pb-1 scrollbar rounded"
        >
          <div className="p-4 rounded-md  text-black font-bold">
            <h3 className="text-lg font-bold text-center">FORM XVI</h3>
            <p className="text-sm font-bold text-center">See Rule 78(a)(i)</p>
            <p className="text-sm font-bold text-center">Muster Roll</p>
            <p className="text-sm">
              Name and Address of Contractor :
              <span className="font-thin text-sm">
              {companydata?.name},{" "}
              {companydata?.address_1}
              </span>
               
            </p>
            <p className="text-sm">
              Nature and location of work : 
              <span className="font-thin text-sm">
              {clientData?.contract_category}
           
              </span>
              
            </p>
            <p className="text-sm">
              Name and address of establishment in/under which contract is
              carried on : 
              <span className="font-thin text-sm">
              {clientData?.address}
              </span>
             
            </p>
            <p className="text-sm">
              Name and address of Principal Employer : 
              <span className="font-thin text-sm">
              {clientData.name},{clientData.address}
              </span>
              
            </p>
            {period && 
            <p className="text-sm">Period: {`${period.toString().slice(0,4)} ${period.toString().slice(4)}`}</p>

            }
            {/* <div className="h-full w-full flex justify-end">
            <button
              onClick={() => markAllPresent(clientData.id)}
              className="px-4 py-2 text-black bg-white border border-black rounded ml-1  text-sm font-medium"
            >
              Mark All Present
            </button>
            </div> */}
            
            {alertMessage && (
          <div className="text-red-500 mb-4">
            <p>{alertMessage}</p>
          </div>
        )}
          </div>
        
 
          <Sheet
            period={period}
            dataset={attendances}
            onTogglePresence={onTogglePresence}
            onSetAdvance={onSetAdvance}
            onGenerateSalary={onGenerateSalary}
            company={company} 
          />
          </section>
      )}
   
    </>
  );
};
 
export default All;
