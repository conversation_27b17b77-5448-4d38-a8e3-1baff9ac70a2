import { Route, Routes, useParams } from "react-router-dom";
import ClientManagementSidebar from "../../components/ClientManagementSidebar";
import All from "./All";
import Sidebar from "../../components/Sidebar";




const Attendance = ({company}) => {


const {companyId} = useParams()

const MAIN_LINKS = [
  { label: "All Clients", route: `/${companyId}/clients` },
  { label: "New Client", route: `/${companyId}/clients/new` },
  {
    label: "Reports",
    subLinks: [
      { label: "Payroll", route: `/${companyId}/payroll` },
      { label: "EPF Details", route: `/${companyId}/payroll/epf-details` },
      { label: "ESI Details", route: `/${companyId}/payroll/ip-details` },
      { label: "Attendance", route: `/${companyId}/payroll/attendance` },
      { label: "Register of Wages", route: `/${companyId}/payroll/wage-register` },
      { label: "Payments", route: `/${companyId}/payroll/payments` },
      { label: "Wage Slip", route: `/${companyId}/payroll/company-wage-slips` },

      
    ],
  },
];

 
  return (
    <div className="w-screen h-screen flex items-stretch">
      {!company ? <ClientManagementSidebar /> :<Sidebar mainLinks={MAIN_LINKS}/>}
      <main className="flex-1 bg-green-500 p-10 overflow-auto">
        <Routes>
          <Route path="" element={<All company={company}  />} />
        </Routes>
      </main>
    </div>
  );
};

export default Attendance;
