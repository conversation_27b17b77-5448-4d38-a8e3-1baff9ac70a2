/* eslint-disable react/prop-types */
import jsPDF from "jspdf";
import "jspdf-autotable"; // this is needed for table in pdf
import * as XLSX from "xlsx";
import { FiDownload } from "react-icons/fi";



const ActionButton = ({ label, color, onClick }) => (
  <button
    className={`px-2 py-0.5 bg-transparent text-${color}-500 rounded border border-${color}-500 hover:bg-${color}-500 hover:text-white duration-150`}
    onClick={onClick}
  >
    {label}
  </button>
);

const ClientList = ({
  clients,
  onEditClient,
  onViewEmployees,
  onViewSalaries,
}) => {
  if (!clients?.length) {
    return (
      <div className="min-h-32 flex justify-center items-center bg-white rounded text-gray-400">
        No clients to display
      </div>
    );
  }
//   const handleExportPDF = () => {
//     const doc = new jsPDF();

//   const tableColumn = [
//     "Name",
//     "Address",
//     "GST",
//     "TAN",
//     "Contract No.",
//     "Contract Value",
//     "Contract Category",
//     "Contract Start Date",
//     "Contract Expiry Date",
//     "Contract Duration",
//     "Days",
//     "EPBG Amount",
//     "EPBG Number",
//     "Client Code",
//     "EPBG Date",
//     "EPBG Expiry Date"
//   ];

//   const tableRows = [];

//   clients.forEach((client) => {
//     const clientData = [
//       client.name,
//       client.address,
//       client.gst,
//       client.tan,
//       client.contract_number,
//       client.contract_value,
//       client.contract_category,
//       client.contract_start_date,
//       client.contract_expiry_date,
//       client.contract_duration,
//       client.days,
//       client.epbg_amount,
//       client.epbg_number,
//       client.client_code,
//       client.epbg_date,
//       client.epbg_expiry_date,
//     ];
//     tableRows.push(clientData);
//   });

//   doc.autoTable({
//     head: [tableColumn],
//     body: tableRows,
//     startY: 20,
//   });

//   doc.save("ClientList.pdf");
// };
    // here you will later add actual PDF export logic
  
  
  const handleExportExcel = () => {
    const worksheetData = clients.map((client) => ({
      Name: client.name,
      Address: client.address,
      GST: client.gst,
      TAN: client.tan,
      "Contract No.": client.contract_number,
      "Contract Value": client.contract_value,
      "Contract Category": client.contract_category,
      "Contract Start Date": client.contract_start_date,
      "Contract Expiry Date": client.contract_expiry_date,
      "Contract Duration": client.contract_duration,
      Days: client.days,
      "EPBG Amount": client.epbg_amount,
      "EPBG Number": client.epbg_number,
      "Client Code": client.client_code,
      "EPBG Date": client.epbg_date,
      "EPBG Expiry Date": client.epbg_expiry_date,
    }));
  
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Clients");
  
    XLSX.writeFile(workbook, "ClientList.xlsx");
    // here you will later add actual Excel export logic
  };
  

  

  return (
    <div className="overflow-x-auto max-w-full">
      <div className="absolute top-2 right-2">
 
  {/* <ActionButton
    label="Export Excel"
    color="yellow"
    onClick={() => handleExportExcel()}
  /> */}
  <button
    onClick={handleExportExcel}
    className="w-1 h-1 rounded-full bg-gray-400 hover:bg-green-400 transition"
    title=""
  >
    
  </button>
</div>

    <table className="bg-white min-w-full divide-y table-fixed text-xs">
      <thead className="border-b bg-gray-100 text-left">
        <tr>
          <th className="px-1 py-1 font-medium">Name</th>
          <th className="px-1 py-1 font-medium">Address</th>
          <th className="px-1 py-1 font-medium">GST</th>
          <th className="px-1 py-1 font-medium">TAN</th>
          <th className="px-1 py-1 font-medium">Contract No.</th>
          <th className="px-1 py-1 font-medium">Contract Value</th>
          <th className="px-1 py-1 font-medium">Contract Category</th>
          <th className="px-1 py-1 font-medium">Contract Start Date</th>
          <th className="px-1 py-1 font-medium">Contract Expiry Date</th>
          <th className="px-1 py-1 font-medium">Contract Duration</th>
          <th className="px-1 py-1 font-medium">Days</th>
          <th className="px-1 py-1 font-medium">EPBG Amount</th>
          <th className="px-1 py-1 font-medium">EPBG Number</th>
          <th className="px-1 py-1 font-medium">Client Code</th>
          <th className="px-1 py-1 font-medium">EPBG Date</th>
          <th className="px-1 py-1 font-medium">EPBG Expiry Date</th>
          <th className="px-1 py-1 w-64 font-medium">Actions</th>
        </tr>
      </thead>
      <tbody className="divide-y">
        {clients.map((item) => (
          <tr key={item.id}>
            <td className="px-3 py-2">
              <span
                onClick={() => onEditClient(item.id)}
                className="cursor-pointer hover:text-green-500"
              >
                {item.name}
              </span>
            </td>
            <td className="px-1 py-">{item.address}</td>
            <td className="px-1 py-1">{item.gst}</td>
      <td className="px-1 py-1">{item.tan}</td>
      <td className="px-1 py-1">{item.contract_number}</td>
      <td className="px-1 py-1">{item.contract_value}</td>
      <td className="px-1 py-1">{item.contract_category}</td>
      <td className="px-1 py-1">{item.contract_start_date}</td>
      <td className="px-1 py-1">{item.contract_expiry_date}</td>
      <td className="px-1 py-1">{item.contract_duration}</td>
      <td className="px-1 py-1">{item.days}</td>
      <td className="px-1 py-1">{item.epbg_amount}</td>
      <td className="px-1 py-1">{item.epbg_number}</td>
      <td className="px-1 py-1">{item.client_code}</td>
      <td className="px-1 py-1">{item.epbg_expiry_date}</td>
      <td className="px-1 py-1">{item.epbg_date}</td>
            <td className="px-3 py-1">
              <div className="flex flex-wrap gap-2 text-sm">
                <ActionButton
                  label="Staff"
                  color="green"
                  onClick={() => onViewEmployees(item.id)}
                />
                <ActionButton
                  label="Wages"
                  color="green"
                  onClick={() => onViewSalaries(item.id)}
                />
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
    </div>
  );
};

export default ClientList;
