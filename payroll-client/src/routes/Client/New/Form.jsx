/* eslint-disable react/prop-types */

import { Form as FormikForm, Field, ErrorMessage } from "formik";
import { FIELDS } from "./constants";

const SelectInput = ({ data }) => (
  <Field
    as="select"
    name={data.name}
    id={data.name}
    className="border px-2.5 py-1.5 rounded-md"
  >
    <option value="" label="Select an option" />
    {data.options.map((opt) => (
      <option key={opt} value={opt}>
        {opt}
      </option>
    ))}
  </Field>
);

const formatDateToDisplay = (isoDate) => {
  if (!isoDate) return "";
  const [year, month, day] = isoDate.split("-");
  return `${day}-${month}-${year}`;
};



const DateInput = ({ data }) => (
  <Field name={data.name}>
    {({ field, form }) => {
      const handleDateChange = (e) => {
        const isoDate = e.target.value;
        form.setFieldValue(data.name, isoDate); 
      };

      const displayValue = field.value
        ? formatDateToDisplay(field.value) 
        : "";

      const isoValue = field.value || ""; 

      return (
        <div className="flex flex-col gap-1">
          <input
            type="date"
            id={data.name}
            value={isoValue}
            onChange={handleDateChange}
            className="border px-2.5 py-1.5 rounded-md opacity-0 absolute pointer-events-none"
          />
          <input
            type="text"
            id={`${data.name}-display`}
            name={data.name}
            value={displayValue}
            readOnly
            placeholder="dd-mm-yyyy"
            className="border px-2.5 py-1.5 rounded-md bg-gray-100 cursor-pointer"
            onClick={() =>
              document.getElementById(data.name).showPicker() 
            }
          />
        </div>
      );
    }}
  </Field>
);

const TextInput = ({ data }) => (
  <Field
    type={data.type}
    name={data.name}
    id={data.name}
    className="border px-2.5 py-1.5 rounded-md"
  />
);

const Form = () => {
  const inputTypeMap = {
    select: SelectInput,
    text: TextInput,
    date: DateInput,
  };

  return (
    <FormikForm>
  <div className="w-[740px] bg-white p-10 px-14 rounded-lg space-y-7 h-full">
    <div className="grid grid-cols-[300px_300px] gap-x-7 gap-y-4">
      {FIELDS.map((item) => {
        const InputFieldComponent = inputTypeMap[item.type] || null;
        return (
          <div key={item.name} className="w-full flex flex-col gap-1">
            <label htmlFor={item.name}>{item.label}</label>
            {InputFieldComponent && <InputFieldComponent data={item} />}
            <span className="text-red-400 text-xs">
              <ErrorMessage name={item.name} />
            </span>
          </div>
        );
      })}
    </div>

    <div className="w-full flex justify-end border-t">
      <button
        type="submit"
        className="bg-green-500 rounded-md px-5 py-1.5 mt-5"
      >
        Save
      </button>
    </div>
  </div>
</FormikForm>

  );
};



export default Form;
