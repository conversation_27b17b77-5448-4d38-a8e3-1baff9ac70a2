import * as yup from "yup";

const contractCategories = ['Cleaning Outcome Based', 'Manpower Outsourcing', 'Facility Outcome Based'];

export const FIELDS = [
  {
    name: "name",
    type: "text",
    label: "Client Name",
  },
  {
    name: "address",
    type: "text",
    label: "Address",
  },
  {
    name: "gst",
    type: "text",
    label: "GST",
  },
  {
    name: "tan",
    type: "text",
    label: "TAN",
  },
  {
    name: "contract_number",
    type: "text",
    label: "Contract No.",
  },
  {
    name: "contract_value",
    type: "text",
    label: "Contract Value",
  },
  {
    name: "contract_category",
    type: "select",
    label: "Contract Category",
    options: contractCategories,
  },
  {
    name: "contract_start_date",
    type: "date",
    label: "Contract Start Date",
  },
  {
    name: "contract_expiry_date",
    type: "date",
    label: "Contract Expiry Date",
  },
  {
    name: "contract_duration",
    type: "text",
    label: "Contract Duration",
  },
  {
    name: "days",
    type: "text",
    label: "Days",
  },
  {
    name: "epbg_amount",
    type: "text",
    label: "EPBG Amount",
  },
  {
    name: "epbg_number",
    type: "text",
    label: "EPBG Number",
  },
  {
    name: "client_code",
    type: "text",
    label: "Client Code",
  },
  {
    name: "epbg_date",
    type: "date",
    label: "EPBG Date",
  },
  {
    name: "epbg_expiry_date",
    type: "date",
    label: "EPBG Expiry Date",
  },
];


export const SCHEMA = yup.object({
  name: yup.string().required("client name is required"),
  address: yup
    .string()
    .required("address is required")
    .min(8, "address should have at least 8 characters"),
  gst: yup.string(),
  tan: yup.string(),
  contract_number: yup.string(),
  contract_value: yup.string().required("contract value is required"),
  contract_category: yup
    .string()
    .oneOf(contractCategories, "Invalid category")
    .required("contract category is required"),
  contract_start_date: yup.date().required("Contract start date is required"),
  contract_expiry_date: yup.date().required("Contract expiry date is required"),
  contract_duration: yup.string().required("contract duration is required"),
  days: yup.number().required("days is required"),
  epbg_amount: yup.string().required("EPBG amount is required"),
  epbg_number: yup.string().required("EPBG number is required"),
  client_code: yup.string().required("Client code is required"),
  epbg_expiry_date: yup.date().required("EPBG expiry date is required"),
  epbg_date: yup.date().required("EPBG date is required"),
});
