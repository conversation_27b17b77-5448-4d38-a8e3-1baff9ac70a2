import { Route, Routes, useParams } from "react-router-dom";
import Sidebar from "../../components/Sidebar";
import All from "./All";
import New from "./New";

const Client = () => {

  const {companyId} = useParams()
  const MAIN_LINKS = [
    { label: "All Clients", route: `/${companyId}/clients` },
    { label: "New Client", route: `/${companyId}/clients/new` },
    {
      label: "Reports",
      subLinks: [
        { label: "Payroll", route: `/${companyId}/payroll` },
        { label: "EPF Details", route: `/${companyId}/payroll/epf-details` },
        { label: "ESI Details", route: `/${companyId}/payroll/ip-details` },
        { label: "Attendance", route: `/${companyId}/payroll/attendance` },
        { label: "Register of Wages", route: `/${companyId}/payroll/wage-register` },
        { label: "Payments", route: `/${companyId}/payroll/payments` },
      { label: "Wage Slip", route: `/${companyId}/payroll/company-wage-slips` },

      ],
    },
  ];
  

  return (
    <div className="w-screen h-full min-h-screen flex items-stretch">
      <Sidebar mainLinks={MAIN_LINKS} />
      <main className="flex-1 bg-green-500 p-10 overflow-auto">
        <Routes>
          <Route path="" element={<All />} />
          <Route path="new" element={<New />} />
          <Route path=":clientId" element={<New />} />
        </Routes>
      </main>
    </div>
  );
};

export default Client;
