/* eslint-disable react/prop-types */
import { useState } from "react";

const ActionButton = ({ label, color, onClick }) => (
  <button
    className={`px-2 py-0.5 bg-transparent text-${color}-500 rounded border border-${color}-500 hover:bg-${color}-500 hover:text-white duration-150`}
    onClick={onClick}
  >
    {label}
  </button>
);

const EmployeeList = ({
  employees,
  selectedStatus,
  onEditEmployee,
  onUpdateStatus,
}) => {
  // Local state to keep track of wage sort order: 'asc' or 'desc'
  const [sortOrder, setSortOrder] = useState("asc");

  // Toggle sort order when heading is clicked
  const handleSortByWage = () => {
    setSortOrder((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  // Sort employees by wage based on current sortOrder
  const sortedEmployees = [...(employees || [])].sort((a, b) => {
    if (sortOrder === "asc") {
      return a.daily_wage - b.daily_wage;
    }
    return b.daily_wage - a.daily_wage;
  });

  if (!employees?.length) {
    return (
      <div className="min-h-32 flex justify-center items-center bg-white rounded text-gray-400">
        No employees to display
      </div>
    );
  }

  return (
    <table className="bg-white w-full rounded overflow-hidden">
      <thead className="border-b bg-gray-100 text-left">
        <tr>
          <th className="px-3 py-1.5 w-44 font-medium">Emp ID</th>
          <th className="px-3 py-1.5 min-w-60 font-medium">Name</th>
          <th className="px-3 py-1.5 w-40 font-medium">Mobile No</th>

          {/* Click on this header to sort by wage */}
          <th
            className="px-3 py-1.5 w-40 font-medium cursor-pointer select-none"
            onClick={handleSortByWage}
          >
            Daily Wage
            <span className="ml-1">
              {sortOrder === "asc" ? "▲" : "▼"}
            </span>
          </th>

          <th className="px-3 py-1.5 font-medium">Actions</th>
        </tr>
      </thead>
      <tbody className="divide-y">
        {sortedEmployees.map((item) => (
          <tr key={item.id}>
            <td className="px-3 py-2">{item.employee_id}</td>
            <td className="px-3 py-2">{item.name}</td>
            <td className="px-3 py-2">{item.mobile_number}</td>
            <td className="px-3 py-2">{item.daily_wage}</td>

            <td className="px-3">
              <div className="flex flex-wrap gap-1 text-sm">
                <ActionButton
                  label="Edit"
                  color="green"
                  onClick={() => onEditEmployee(item.id)}
                />
                {selectedStatus === "ACTIVE" ? (
                  <ActionButton
                    label="Deactivate"
                    color="red"
                    onClick={() => onUpdateStatus(item.id, "INACTIVE")}
                  />
                ) : (
                  <ActionButton
                    label="Activate"
                    color="green"
                    onClick={() => onUpdateStatus(item.id, "ACTIVE")}
                  />
                )}
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default EmployeeList;
