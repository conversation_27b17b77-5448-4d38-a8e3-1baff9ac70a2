import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import employeesApi from "../../../apis/employees";
import PageLoader from "../../../components/PageLoader";
import EmployeeList from "./EmployeeList";

const STATUS_OPTIONS = ["ACTIVE", "INACTIVE"];

const All = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [employees, setEmployees] = useState();
  const [status, setStatus] = useState("ACTIVE");

  const { clientId } = useParams();
  const navigate = useNavigate();

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const { data } = await employeesApi.fetchAll(clientId, status);
      setEmployees(data.employees);
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const onEditEmployee = (employeeId) => {
    navigate(`${employeeId}`);
  };

  const onUpdateStatus = async (employeeId, status) => {
    setIsLoading(true);
    try {
      await employeesApi.updateStatus(employeeId, status);
      await fetchData();
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [status]);

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <>
      <header className="flex justify-between items-center mb-5">
        <h2 className="text-2xl font-semibold">Staff list</h2>
        <select
          value={status}
          onChange={(e) => setStatus(e.target.value)}
          className="border px-1 py-0.5 rounded-md"
        >
          {STATUS_OPTIONS.map((opt) => (
            <option key={opt} value={opt}>
              {opt}
            </option>
          ))}
        </select>
      </header>
      <EmployeeList
        employees={employees}
        selectedStatus={status}
        onEditEmployee={onEditEmployee}
        onUpdateStatus={onUpdateStatus}
      />
    </>
  );
};

export default All;
