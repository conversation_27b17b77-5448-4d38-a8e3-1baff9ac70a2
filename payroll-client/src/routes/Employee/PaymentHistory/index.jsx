const PaymentHistory = () => {
  // Sample data for the payment history
  const paymentHistoryData = [
    {
      slNo: 1,
      employeeName: "John Doe",
      destination: "XYZ Factory",
      dailyWages: 200,
      workingDays: 26,
      overpaymentRecovery: 100,
      minimumWage: 5000,
      employeeEPF: 12,
      employeeESI: 0.75,
      employerEPF: 13,
      employerESI: 3.25,
      grossSalary: 5200,
      totalAmountPayable: 5400,
      netSalaryPaid: 4800,
      signature: "Signature 1",
    },
    {
      slNo: 2,
      employeeName: "Alice Smith",
      destination: "ABC Corp",
      dailyWages: 250,
      workingDays: 20,
      overpaymentRecovery: 50,
      minimumWage: 6000,
      employeeEPF: 12,
      employeeESI: 0.75,
      employerEPF: 13,
      employerESI: 3.25,
      grossSalary: 5500,
      totalAmountPayable: 5700,
      netSalaryPaid: 5300,
      signature: "Signature 2",
    },
  ];

  return (
    <div className="p-4 text-white">
      <div className="border border-gray-300 p-4">
        <h2 className="text-center font-bold text-xl mb-4">Payment History</h2>

        {/* Payment History Table */}
        <table className="min-w-full table-auto border-collapse border border-gray-200">
          <thead>
            <tr>
              <th className="px-4 py-2 border border-gray-300">SL No</th>
              <th className="px-4 py-2 border border-gray-300">
                Name of Employee
              </th>
              <th className="px-4 py-2 border border-gray-300">
                Destination of Employee
              </th>
              <th className="px-4 py-2 border border-gray-300">Daily Wages</th>
              <th className="px-4 py-2 border border-gray-300">
                No of Working Days
              </th>
              <th className="px-4 py-2 border border-gray-300">
                Overpayment Recovery (if any)
              </th>
              <th className="px-4 py-2 border border-gray-300">Minimum Wage</th>

              {/* Employee Contribution Columns */}
              <th
                colSpan="2"
                className="px-4 py-2 border border-gray-300 text-center"
              >
                Employee Contribution
              </th>

              {/* Employer Contribution Columns */}
              <th
                colSpan="2"
                className="px-4 py-2 border border-gray-300 text-center"
              >
                Employer Contribution
              </th>

              <th className="px-4 py-2 border border-gray-300">Gross Salary</th>
              <th className="px-4 py-2 border border-gray-300">
                Total Amount Payable
              </th>
              <th className="px-4 py-2 border border-gray-300">
                Net Salary Paid
              </th>
              <th className="px-4 py-2 border border-gray-300">Signature</th>
            </tr>
            <tr>
              {/* Sub-Headers for Employee and Employer Contribution */}
              <th className="px-4 py-2 border border-gray-300">EPF 12%</th>
              <th className="px-4 py-2 border border-gray-300">ESI 0.75%</th>
              <th className="px-4 py-2 border border-gray-300">EPF 13%</th>
              <th className="px-4 py-2 border border-gray-300">ESI 3.25%</th>
            </tr>
          </thead>
          <tbody>
            {paymentHistoryData.map((data, index) => (
              <tr key={index}>
                <td className="px-4 py-2 border border-gray-300">
                  {data.slNo}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.employeeName}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.destination}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.dailyWages}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.workingDays}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.overpaymentRecovery}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.minimumWage}
                </td>

                {/* Employee Contribution */}
                <td className="px-4 py-2 border border-gray-300">
                  {data.employeeEPF}%
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.employeeESI}%
                </td>

                {/* Employer Contribution */}
                <td className="px-4 py-2 border border-gray-300">
                  {data.employerEPF}%
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.employerESI}%
                </td>

                <td className="px-4 py-2 border border-gray-300">
                  {data.grossSalary}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.totalAmountPayable}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.netSalaryPaid}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {data.signature}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PaymentHistory;
