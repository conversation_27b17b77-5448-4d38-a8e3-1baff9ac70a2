const SalaryTransfer = () => {
  const employees = [
    {
      id: 1,
      sbiDescription1: "Salary Payment",
      sbiDescription2: "Monthly",
      sbiDescription3: "2024",
      salary: 50000,
      name: "<PERSON>",
      ifsc: "IFSC001234",
      accountNumber: "****************",
    },
    {
      id: 2,
      sbiDescription1: "Bonus Payment",
      sbiDescription2: "Yearly",
      sbiDescription3: "2024",
      salary: 10000,
      name: "<PERSON>",
      ifsc: "IFSC001235",
      accountNumber: "****************",
    },
    {
      id: 3,
      sbiDescription1: "Salary Payment",
      sbiDescription2: "Monthly",
      sbiDescription3: "2024",
      salary: 60000,
      name: "<PERSON>",
      ifsc: "IFSC001236",
      accountNumber: "****************",
    },
  ];

  // Calculate the total salary
  const totalSalary = employees.reduce(
    (acc, employee) => acc + employee.salary,
    0
  );
  const companyname = "image facility management pvt.";
  const tabletitle = "salary transfer details of wage month january 2025";

  return (
    <div className="bg-green-500 text-white overflow-x-auto">
      <div className="border  ">
        <p className="text-center font-bold m-2 uppercase">{companyname}</p>
        <hr></hr>
        <p className="text-center font-bold m-2 uppercase">{tabletitle}</p>
        <table className="min-w-full table-auto border-collapse border border-gray-200">
          <thead>
            <tr>
              <th className="px-4 py-2 border border-gray-300">S.No</th>
              <th
                colSpan="3"
                className="px-4 py-2 border border-gray-300 text-center"
              >
                SBI Description
              </th>
              <th className="px-4 py-2 border border-gray-300">Salary</th>
              <th className="px-4 py-2 border border-gray-300">Name</th>
              <th className="px-4 py-2 border border-gray-300">IFSC</th>
              <th className="px-4 py-2 border border-gray-300">
                Account Number
              </th>
            </tr>
          </thead>
          <tbody>
            {employees.map((employee) => (
              <tr key={employee.id}>
                <td className="px-4 py-2 border border-gray-300">
                  {employee.id}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {employee.sbiDescription1}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {employee.sbiDescription2}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {employee.sbiDescription3}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {employee.salary}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {employee.name}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {employee.ifsc}
                </td>
                <td className="px-4 py-2 border border-gray-300">
                  {employee.accountNumber}
                </td>
              </tr>
            ))}
            <tr>
              <td className="px-4 py-2 border border-gray-300" colSpan="4">
                Total Salary
              </td>
              <td className="px-4 py-2 border border-gray-300">
                {totalSalary}
              </td>
              <td colSpan="3" className="px-4 py-2 border border-gray-300"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SalaryTransfer;
