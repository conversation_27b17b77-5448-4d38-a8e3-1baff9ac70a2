import { Route, Routes } from "react-router-dom";
import ClientManagementSidebar from "../../components/ClientManagementSidebar";
import All from "./All";
import New from "./New";

const Employee = () => {
  return (
    <div className="w-screen h-screen flex items-stretch">
      <ClientManagementSidebar />
      <main className="flex-1 bg-green-500 p-10 overflow-auto">
        <Routes>
          <Route path="" element={<All />} />
          <Route path="new" element={<New />} />
          <Route path=":employeeId" element={<New />} />
        </Routes>
      </main>
    </div>
  );
};

export default Employee;
