import React, { useState, useEffect, useCallback, useRef } from "react";
import { debounce } from "lodash";
import salariesApi from "../../apis/salaries";
import PageLoader from "../../components/PageLoader";
import * as XLSX from "xlsx";
import { formatDate, getCurrentYearMonth } from "../../utils/datetime";
import DateFilter from "../Payment/All/DateFilter";
import EditEpfModal from "../PayrollManagement/EditEpfDetails";
import { useParams } from "react-router-dom";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
const monthMap = {
  January: "01",
  February: "02",
  March: "03",
  April: "04",
  May: "05",
  June: "06",
  July: "07",
  August: "08",
  September: "09",
  October: "10",
  November: "11",
  December: "12",
};

const EPFDetails = () => {
  const [activeTab, setActiveTab] = useState(1);
  const [period, setPeriod] = useState(getCurrentYearMonth());
  const [selectedMonth, setSelectedMonth] = useState();
  const [selectedYear, setSelectedYear] = useState();
  const [salaryData, setSalaryData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalGrossAmount, setTotalGrossAmount] = useState(true);
  const companyData = JSON.parse(localStorage.getItem("selected-company"));
  const [selectedItem, setSelectedItem] = useState(null);
  const [isIpEditModalOpen, setisIpEditModalOpen] = useState(false);
  const [isEpfEditModalOpen, setisEpfEditModalOpen] = useState(false);
  const { companyId } = useParams();



  const handleOpenEpfModal = (item) => {
    setSelectedItem(item);
    setisEpfEditModalOpen(true);
  };
  const handleCloseEpfModal = () => {
    setisEpfEditModalOpen(false);
    setSelectedItem(null);
  };


  const fetchSalaryData = async () => {
    setIsLoading(true);
    try {
      if(!companyId) return

      const { data } = await salariesApi.getSalaryData({ period,companyId });
      setSalaryData(data.salaries || []);
    } catch (error) {
      if (error.response?.status === 404) {
        setSalaryData([]);
      }
      console.error("Error fetching salary data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const totalGrossAmount = salaryData.reduce(
      (total, item) => total + (item.net_amount || 0),
      0
    );
    setTotalGrossAmount(totalGrossAmount);
  }, [salaryData]);

  useEffect(() => {
    const year = period.toString().slice(0, 4);
    const month = period.toString().slice(4, 6);
    fetchSalaryData();
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const monthName = months[parseInt(month, 10) - 1];
    setSelectedMonth(monthName);
    setSelectedYear(year);
  }, [period]);

 


 

  const handleExport = (caseType) => {
    let sheetData;
  
    switch (caseType) {
  
      case 1: 
        sheetData = salaryData
        .filter((item) => item?.employee?.epf_uan && item?.employee?.epf_uan !== "0")
        .map((item, i) => ({
          "S. No": i + 1,
          UAN: item?.employee?.epf_uan || "",
          "Member Name": item?.employee?.name || "",
          "EPS Wages": item?.gross_amount > 15000 ? "15,000" : item?.gross_amount?.toLocaleString() || "0",
          "NCP Days": item?.absent_days || "0",
        }));
      break;
  
    
  
      
  
      default:
        console.error("Unsupported case type");
        return;
    }
  
    const ws = XLSX.utils.json_to_sheet(sheetData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `Export - ${selectedMonth}`);
  
    XLSX.writeFile(wb, `Export_${selectedMonth}.xlsx`);
  };





  const handleSaveEpfDetails = async (updatedItem) => {
    const updatedSalaryData = salaryData.map((item) =>
      item.id === updatedItem.id ? updatedItem : item
    );
    try {
      await salariesApi.updateEpfData(updatedItem.id, updatedItem);
      setSalaryData(updatedSalaryData);
    } catch (error) {
      console.error("Error updating salary:", error);
    }
  };


  const downloadPDF = () => {
    const doc = new jsPDF('l', 'mm', 'a4'); 
  
    doc.setFontSize(16);
    doc.text(companyData.name, doc.internal.pageSize.width / 2, 15, { align: 'center' });
  
    doc.setFontSize(12);
    doc.text(
      `EPF DETAILS of MONTH ${selectedMonth?.toUpperCase()} - ${selectedYear}`,
      doc.internal.pageSize.width / 2,
      25,
      { align: 'center' }
    );
  
    const tableData = salaryData
    .filter((item) => item?.employee?.epf_uan && item?.employee?.epf_uan !== "0")
    .map((item, i) => [
      i + 1,
      item?.employee?.epf_uan || "-",
      item?.employee?.name || "-",
      item?.gross_amount > 15000 ? "15,000" : item?.gross_amount || "0",
      item?.absent_days || "0",
    ]);
    doc.autoTable({
      startY: 35,
      head: [['S.no', 'UAN', 'Member Name', 'EPS Wages', 'NCP Days']], 
      body: tableData,
      theme: 'grid',
      styles: { fontSize: 8, cellPadding: 2 },
      headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' }, 
    });
  
    doc.save(`EPF_Contribution_Details_${selectedMonth}.pdf`);
  };
  
  

  const Tables = () => {
    switch (activeTab) {
     
      case 1:
        return (
          <div className="w-full p-6 text-gray-900">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-3">{companyData.name}</h2>
              <p className="text-base">
                EPF DETAILS of MONTH {selectedMonth?.toLocaleUpperCase()} {" - "} {selectedYear}
              </p>
              <button
                className="px-4 py-2 text-black bg-white border border-black rounded ml-1  text-sm font-medium"
                onClick={() => handleExport(1)}
              >
                Export to Excel
              </button>
              <button
          onClick={downloadPDF}
          className="px-4 py-2 text-black bg-white border border-black rounded ml-1  text-sm font-medium"
        >
          Download PDF
        </button>
            </div>
            <div className="overflow-x-auto">
              <table id="epf-table" className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border p-3 text-left text-base font-semibold">S.no</th>
                    <th className="border p-3 text-left text-base font-semibold">UAN</th>
                    <th className="border p-3 text-left text-base font-semibold">Member Name</th>
                    <th className="border p-3 text-left text-base font-semibold">EPS Wages</th>
                    <th className="border p-3 text-left text-base font-semibold">NCP Days</th>
                    <th className="border p-3 text-left text-base font-semibold hide-in-pdf">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {salaryData
                  .filter((item) => item?.employee?.epf_uan && item?.employee?.epf_uan !== "0")
                  .map((item, i) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="border p-3 text-sm">{i + 1}</td>
                      <td className="border p-3 text-sm font-mono">{item.employee?.epf_uan}</td>
                      <td className="border p-3 text-sm">{item.employee?.name}</td>
                      <td className="border p-3 text-sm">{item.gross_amount > 15000 ? "15,000" : item.gross_amount}</td>
                      <td className="border p-3 text-sm">{item.absent_days}</td>
                      <td className="border p-3 hide-in-pdf">
                        <button
                          onClick={() => handleOpenEpfModal(item)}
                          className="bg-green-500 text-white px-4 py-2 rounded text-sm font-medium"
                        >
                          Edit
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
  
              <EditEpfModal
                isOpen={isEpfEditModalOpen}
                onClose={handleCloseEpfModal}
                selectedItem={selectedItem}
                onSave={handleSaveEpfDetails}
              />
            </div>
          </div>
        );
  
     
    
  
      default:
        return null;
    }
  };
  
  if (isLoading) {
    return <PageLoader />;
  }
  
  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-green-500 text-black py-8 px-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold mb-3 text-black">
              Payroll Management
            </h1>
            <p className="text-lg text-black">
              Current Period: {selectedMonth}
            </p>
          </div>
          <DateFilter period={period} setPeriod={setPeriod} />
        </div>
      </div>
  
      {/* Main Content */}
      <div className="p-6">
        <div className="rounded-lg overflow-hidden shadow-sm">
          
          <div className="bg-white">
            <Tables />
          </div>
        </div>
      </div>
    </div>
  )
};

export default EPFDetails;
