/* eslint-disable react/prop-types */
const PLACEHOLDER_URL = "https://placehold.co/400x400/000000/FFF";

const CompanyList = ({ companies, onSelect }) => (
  <div className="w-screen min-h-screen bg-green-500 flex flex-col justify-center items-center gap-10 p-10">
    <header>
      <h2 className="font-bold text-2xl">Select a company</h2>
    </header>
    <main className="max-w-full flex flex-wrap gap-10 justify-center">
      {companies?.map((item) => (
        <div
          key={item.id}
          className="w-56 h-52 flex flex-col items-center gap-2 group hover:scale-105 duration-150 cursor-pointer"
          onClick={() => onSelect(item)}
        >
          <div className="w-48 h-48 overflow-hidden rounded-lg bg-white">
            <img 
              src={item.logo_url ?? PLACEHOLDER_URL} 
              className="w-full h-full object-cover"
              alt={item.name}
            />
          </div>
          <h3 className="text-white w-full text-center text-md group-hover:font-medium truncate px-2">
            {item.name}
          </h3>
        </div>
      ))}
    </main>
  </div>
);

export default CompanyList;
