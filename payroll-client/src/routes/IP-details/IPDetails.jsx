import React, { useState, useEffect, useCallback, useRef } from "react";
import { debounce } from "lodash";
import salariesApi from "../../apis/salaries";
import PageLoader from "../../components/PageLoader";
import * as XLSX from "xlsx";
import { formatDate, getCurrentYearMonth } from "../../utils/datetime";
import DateFilter from "../Payment/All/DateFilter";
import EditIPDetails from "../PayrollManagement/EditIPDetails";
import { useParams } from "react-router-dom";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
const monthMap = {
  January: "01",
  February: "02",
  March: "03",
  April: "04",
  May: "05",
  June: "06",
  July: "07",
  August: "08",
  September: "09",
  October: "10",
  November: "11",
  December: "12",
};

const IPDetails = () => {
  const [activeTab, setActiveTab] = useState(2);
  const [period, setPeriod] = useState(getCurrentYearMonth());
  const [selectedMonth, setSelectedMonth] = useState();
  const [selectedYear, setSelectedYear] = useState();
  const [salaryData, setSalaryData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalGrossAmount, setTotalGrossAmount] = useState(true);
  const companyData = JSON.parse(localStorage.getItem("selected-company"));
  const [selectedItem, setSelectedItem] = useState(null);
  const [isIpEditModalOpen, setisIpEditModalOpen] = useState(false);
  const [isEpfEditModalOpen, setisEpfEditModalOpen] = useState(false);
  const { companyId } = useParams();

  const handleOpenModal = (item) => {
    setSelectedItem(item);
    setisIpEditModalOpen(true);
  };

  const handleOpenEpfModal = (item) => {
    setSelectedItem(item);
    setisEpfEditModalOpen(true);
  };
  const handleCloseEpfModal = () => {
    setisEpfEditModalOpen(false);
    setSelectedItem(null);
  };
  const handleCloseModal = () => {
    setisIpEditModalOpen(false);
    setSelectedItem(null);
  };

  const fetchSalaryData = async () => {
    setIsLoading(true);
    try {
      if(!companyId) return

      const { data } = await salariesApi.getSalaryData({ period,companyId });
      setSalaryData(data.salaries || []);
    } catch (error) {
      if (error.response?.status === 404) {
        setSalaryData([]);
      }
      console.error("Error fetching salary data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const totalGrossAmount = salaryData.reduce(
      (total, item) => total + (item.net_amount || 0),
      0
    );
    setTotalGrossAmount(totalGrossAmount);
  }, [salaryData]);

  useEffect(() => {
    const year = period.toString().slice(0, 4);
    const month = period.toString().slice(4, 6);
    fetchSalaryData();
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const monthName = months[parseInt(month, 10) - 1];
    setSelectedMonth(monthName);
    setSelectedYear(year);
  }, [period]);

  const createDebouncedUpdate = () =>
    debounce((id, field, value) => {
      console.log("Debounced update:", { id, field, value });
    }, 500);

  const debouncedUpdate = useCallback(createDebouncedUpdate(), []);

  const handleInputChange = (e, id, field) => {
    const value = e.target.value;
    setEpfData((prevData) =>
      prevData.map((item) =>
        item.id === id ? { ...item, [field]: value } : item
      )
    );
    debouncedUpdate(id, field, value);
  };

  const handleExport = (caseType) => {
    let sheetData;
  
    switch (caseType) {
      case 2: 
        sheetData = salaryData
        .filter((item) => item?.employee?.esi_number !== "0")
        .map((item, i) => ({
          "S. No": i + 1,
          "IP Number": item?.employee?.esi_number || "",
          "IP Name": item?.employee?.name || "",
          "Wages Paid Days": item?.present_days || "0",
          "Total Monthly Wages": item?.gross_amount > 21000 ? 21000 : item?.gross_amount || "0",
          "Reason Code": item?.reason_code || "",
          "Last Working Day": item?.last_working_day
            ? formatDate(item?.last_working_day)
            : "",
        }));
        break;
  
      
  
      default:
        console.error("Unsupported case type");
        return;
    }
  
    const ws = XLSX.utils.json_to_sheet(sheetData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `Export - ${selectedMonth}`);
  
    XLSX.writeFile(wb, `Export_${selectedMonth}.xlsx`);
  };



  const handleSaveIpDetails = async (updatedItem) => {
    const updatedSalaryData = salaryData.map((item) =>
      item.id === updatedItem.id ? updatedItem : item
    );
    console.log("Updated Salary Data:", updatedSalaryData);
  
    try {
      await salariesApi.updateSalaryData(updatedItem.id, updatedItem);
      console.log("Updated Salary Data:", updatedSalaryData);
  
      setSalaryData(updatedSalaryData);
    } catch (error) {
      console.error("Error updating salary:", error);
    }
  };

  const downloadPDF = () => {
    const doc = new jsPDF('l', 'mm', 'a4'); 
  
    doc.setFontSize(16);
    doc.text(companyData.name, doc.internal.pageSize.width / 2, 15, { align: 'center' });
  
    doc.setFontSize(12);
    doc.text(
      `IP Detail of MONTH ${selectedMonth?.toUpperCase()} - ${selectedYear}`,
      doc.internal.pageSize.width / 2,
      25,
      { align: 'center' }
    );
  
    const tableData = salaryData
    .filter((item) => item?.employee?.esi_number !== "0")
    .map((item, index) => [
      index + 1,
      item?.employee?.esi_number || "-",
      item?.employee?.name || "-",
      // item?.employee?.contribution || "0",
      item?.present_days || "0",
      item?.gross_amount > 21000 ? 21000 : item?.gross_amount || "0",
      item?.reason_code || "-",
      item?.last_working_day ? formatDate(item?.last_working_day) : "-",
    ]);
  
    doc.autoTable({
      startY: 35,
      head: [
        [
          'S.no',
          'IP Number (10 Digits)',
          'IP Name',
          // 'Contribution',
          'No. of Days Paid',
          'Total Monthly Wages',
          'Reason Code (Zero Days)',
          'Last Working Day',
        ],
      ],
      body: tableData,
      theme: 'grid',
      styles: { fontSize: 8, cellPadding: 2 },
      headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' }, 
    });
  
    doc.save(`IP_Contribution_Details_${selectedMonth}.pdf`);
  };

  const Tables = () => {
    switch (activeTab) {

      case 2:
        return (
          <div className="w-full p-6 text-gray-900">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold mb-3">{companyData.name}</h2>
            <p className="text-base">
              ESI DETAILS of MONTH {selectedMonth?.toUpperCase()} - {selectedYear}
            </p>
            <button
          onClick={downloadPDF}
          className="px-4 py-2 text-black bg-white border border-black rounded ml-1  text-sm font-medium"
        >
          Download PDF
        </button>
            <button
              className="px-4 py-2 text-black bg-white border border-black rounded ml-1 text-sm font-medium"
              onClick={() => handleExport(2)}
            >
              Export to Excel
            </button>
          </div>
          <div className="overflow-x-auto">
            <table id="ip-table" className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border p-3 text-left text-base font-semibold">S.no</th>
                  <th className="border p-3 text-left text-base font-semibold">IP Number (10 Digits)</th>
                  <th className="border p-3 text-left text-base font-semibold">IP Name</th>
                  {/* <th className="border p-3 text-left text-base font-semibold">Contribution</th> */}
                  <th className="border p-3 text-left text-base font-semibold">
                    No of Days for which wages paid/payable during the month
                  </th>
                  <th className="border p-3 text-left text-base font-semibold">Total Monthly Wages</th>
                  <th className="border p-3 text-left text-base font-semibold">
                    Reason Code for Zero working days
                  </th>
                  <th className="border p-3 text-left text-base font-semibold">Last Working Day</th>
                  <th className="border p-3 text-left text-base font-semibold hide-in-pdf">Action</th> {/* Hide in PDF */}
                </tr>
              </thead>
              <tbody>
                {salaryData
                .filter((item) => item?.employee?.esi_number !== "0")
                .map((item, index) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="border p-3 text-sm">{index + 1}</td>
                    <td className="border p-3 text-sm font-mono">{item?.employee?.esi_number}</td>
                    <td className="border p-3 text-sm">{item?.employee?.name}</td>
                    {/* <td className="border p-3 text-sm">{item?.employee?.contribution}</td> */}
                    <td className="border p-3 text-sm">{item?.present_days}</td>
                    <td className="border p-3 text-sm">{item?.gross_amount > 21000 ? 21000 : item?.gross_amount}</td>
                    <td className="border p-3 text-sm">{item?.reason_code}</td>
                    <td className="border p-3 text-sm">
                      {item?.last_working_day ? formatDate(item?.last_working_day) : null}
                    </td>
                    <td className="border p-3 hide-in-pdf"> {/* Hide in PDF */}
                      <button
                        onClick={() => handleOpenModal(item)}
                        className="bg-green-500 text-white px-4 py-2 rounded text-sm font-medium"
                      >
                        Edit
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
        
            <EditIPDetails
              isOpen={isIpEditModalOpen}
              onClose={handleCloseModal}
              selectedItem={selectedItem}
              onSave={handleSaveIpDetails}
            />
          </div>
        </div>
        
        );
      case 3:
      
          return (
            <div className="w-full p-6 text-gray-900">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold mb-3">{companyData.name}</h2>
                <p className="text-base">
                  Contribution Detail of MONTH {selectedMonth?.toUpperCase()} - {selectedYear}
                </p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border p-3 text-left text-base font-semibold">S.no</th>
                      <th className="border p-3 text-left text-base font-semibold">IP Name</th>

                      {/* <th className="border p-3 text-left text-base font-semibold">Contribution</th> */}
                    </tr>
                  </thead>
                  <tbody>
                    {salaryData.map((item, index) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="border p-3 text-sm">{index + 1}</td>
                        
                        <td className="border p-3 text-sm">{item?.employee?.name}</td>
                        {/* <td className="border p-3 text-sm">{item?.employee?.contribution}</td> */}
                       
                       
                      </tr>
                    ))}
                  </tbody>
                </table>
    
                <EditIPDetails
                  isOpen={isIpEditModalOpen}
                  onClose={handleCloseModal}
                  selectedItem={selectedItem}
                  onSave={handleSaveIpDetails}
                />
              </div>
            </div>
          );
    
  
      default:
        return null;
    }
  };
  
  if (isLoading) {
    return <PageLoader />;
  }
  
  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-green-500 text-black py-8 px-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold mb-3 text-black">
              Payroll Management
            </h1>
            <p className="text-lg text-black">
              Current Period: {selectedMonth}
            </p>
          </div>
          <DateFilter period={period} setPeriod={setPeriod} />
        </div>
      </div>
  
      {/* Main Content */}
      <div className="p-6">
        <div className="rounded-lg overflow-hidden shadow-sm">
         
          <div className="bg-white">
            <Tables />
          </div>
        </div>
      </div>
    </div>
  )
};

export default IPDetails;
