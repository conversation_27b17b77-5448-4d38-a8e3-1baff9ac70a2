import { Form as FormikForm, Field, ErrorMessage } from "formik";
import { FIELDS } from "./constants";
import logo from "./assets/ifm'.png";
import background from "./assets/ifm.jpg";



const Form = () => {
  return (
    <FormikForm>
      <div className="min-w-screen min-h-screen bg-green-500 flex flex-col justify-center items-center bg-cover bg-center"
      style={{backgroundImage:`url(${background})`}}>
      <img
          src={logo}// Use the imported logo
          alt="Logo"
          className="mb-5 w-30 h-24 rounded-xl" // Adjust styling as needed
        />
      
      <div>
        <i class="fa-solid fa-user text-green-500 text-6xl mb-5"></i>
        </div>
        <div className="bg-white w-full max-w-[400px] p-10 px-15 flex flex-col gap-4 rounded-lg">
          {FIELDS.map((item) => (
            <div key={item.name} className="w-full flex flex-col gap-1">
              <label htmlFor={item.name}>{item.label}</label>
              <Field
                type={item.type}
                name={item.name}
                className="border px-2.5 py-1.5 rounded-md border-green-500"
              />
              <ErrorMessage name={item.name} />
            </div>
          ))}

          <button
            type="submit"
            className="bg-green-500 rounded-md px-4 py-1.5 mt-5 w-auto mx-auto text-center"
          >
            Login
          </button>
        </div>
      </div>
    </FormikForm>
  );
};

export default Form;
