/* eslint-disable react/prop-types */

import { useMemo } from "react";
import { getYearOptions, getMonthOptions } from "../../../utils/datetime";

const DateFilter = ({ period, setPeriod }) => {
  const selectedYear = useMemo(() => Math.trunc(period / 100), [period]);
  const selectedMonth = useMemo(
    () => period - Math.trunc(period / 100) * 100,
    [period]
  );
  const yearOptions = getYearOptions();
  const monthOptions = useMemo(
    () => getMonthOptions(Math.trunc(period / 100)),
    [period]
  );

  const onYearChange = (e) => {
    const newYear = Number(e.target.value);
    const newMonth =
      newYear === new Date().getFullYear()
        ? Math.max(selectedMonth, new Date().getMonth() + 1)
        : selectedMonth;
    setPeriod(newYear * 100 + newMonth);
  };

  const onMonthChange = (e) => {
    const newMonth = Number(e.target.value);
    setPeriod(selectedYear * 100 + newMonth);
  };

  return (
    <div className="flex gap-2">
      <select
        value={selectedYear}
        onChange={onYearChange}
        className="border px-1 py-0.5 rounded-md"
      >
        {yearOptions.map((opt) => (
          <option key={opt.value} label={opt.label} value={opt.value} />
        ))}
      </select>
      <select
        value={selectedMonth}
        onChange={onMonthChange}
        className="border px-1 py-0.5 rounded-md"
      >
        {monthOptions.map((opt) => (
          <option key={opt.value} label={opt.label} value={opt.value} />
        ))}
      </select>
    </div>
  );
};

export default DateFilter;
