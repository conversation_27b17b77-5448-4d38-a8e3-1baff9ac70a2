/* eslint-disable react/prop-types */
import React from "react";


const formatNumber = (value) => {
  if (typeof value === "number") {
    return value.toFixed(1);
  }
  const parsed = parseFloat(value);
  return isNaN(parsed) ? value : parsed.toFixed(1);
};

const Table = ({ salaries }) => {
 


  return (
    <div>
     

      <div id="salary-table" className="overflow-auto">
        <table className="bg-white w-full rounded border-collapse">
          <thead className="border-b bg-gray-100 text-left text-sm leading-tight">
            <tr>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Sl No.</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Employee Name</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Designation</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Daily wages</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Allowance</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Bonus</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">No. of working days</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Overpayment recovery (if any)</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Minimum wage</th>
              <th className="border px-2.5 py-1 font-medium" colSpan="2">Employee&apos;s contribution</th>
              <th className="border px-2.5 py-1 font-medium" colSpan="2">Employer&apos;s contribution</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Gross salary</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Service charge</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Total bill amount (taxable)</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Net salary paid</th>
              <th className="border px-2.5 py-1 font-medium" rowSpan="2">Signature</th>
            </tr>
            <tr>
              <th className="border px-2.5 py-1 font-medium">EPF 12%</th>
              <th className="border px-2.5 py-1 font-medium">ESI 0.75%</th>
              <th className="border px-2.5 py-1 font-medium">EPF 13%</th>
              <th className="border px-2.5 py-1 font-medium">ESI 3.25%</th>
            </tr>
          </thead>

          <tbody className="divide-y">
            {salaries?.map((item, index) => (
              <tr key={index}>
                <td className="px-2.5 py-1.5 border">{index + 1}</td>
                <td className="px-2.5 py-1.5 border">{item.employee.name}</td>
                <td className="px-2.5 py-1.5 border">{item.employee.designation || ""}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.daily_rate)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.employee.allowance * item.salary.days_worked) || 0}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.employee.bonus * item.salary.days_worked) || 0}</td>

                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.days_worked)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.overpayment_recovery || 0)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.gross_amount)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.epf)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.esi)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.epf_employer)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.esi_employer)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.actual_gross_amount)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.service_charge)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.billable_amount)}</td>
                <td className="px-2.5 py-1.5 border">{formatNumber(item.salary.net_amount) }</td>
                <td className="px-2.5 py-1.5 border"> {/* Signature Field */}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Table;
