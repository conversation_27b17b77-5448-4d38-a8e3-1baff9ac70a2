import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import salariesApi from "../../../apis/salaries";
import PageLoader from "../../../components/PageLoader";
import Error from "../../../components/Error";
import { getCurrentYearMonth } from "../../../utils/datetime";
import { getWagesRegisterData } from "../../../utils/salary";
import DateFilter from "./DateFilter";
import Table from "./Table";
import * as XLSX from "xlsx";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { formatPeriod } from "../../../utils/utils";

const All = ({company}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [period, setPeriod] = useState(getCurrentYearMonth());
  const [salaries, setSalaries] = useState();
  const companydata = JSON.parse(localStorage.getItem("selected-company"));
  const clientData = JSON.parse(localStorage.getItem("selected-client"));


  const navigate = useNavigate();
  const { clientId,companyId } = useParams();

  const fetchData = async () => {
    setIsLoading(true);
    try {
      let data;
      if(company) {
        const res = await salariesApi.companyFetchAll({
          companyId,
          period,
        });
        data = res.data;
        // Transform data for company view
        const transformedData = data.salaries.map(clientGroup => ({
          client: clientGroup.client,
          employees: getWagesRegisterData(clientGroup.employees)
        }));
        setSalaries(transformedData);
      } else {
        const res = await salariesApi.fetchAll({
          clientId,
          period,
        });
        data = res.data;
        setSalaries(getWagesRegisterData(data.salaries));
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const onViewInvoice = () => {
    navigate(`${period}`);
  };

  useEffect(() => {
   fetchData();
  }, [clientId,period,companyId]);

  if (isLoading) {
    return <PageLoader />;
  }

  if (!salaries) {
    return <Error title="Could not load salaries" />;
  }

  const handleExport = () => {
    if (company) {
      const workbook = XLSX.utils.book_new();
      
      salaries.forEach((clientGroup) => {
        const sheetData = clientGroup.employees.map((item, idx) => ({
          "Sl No.": idx + 1,
          "Client": clientGroup.client.name,
          "Employee Name": item.employee.name,
          "Employee ID": item.employee.employee_id,
          "Daily wages": formatNumber(item.salary.daily_rate),
          "No. of working days": formatNumber(item.salary.days_worked),
          "Overpayment recovery": formatNumber(0),
          "Minimum wage": formatNumber(item.salary.gross_amount),
          "EPF 12%": formatNumber(item.salary.epf),
          "ESI 0.75%": formatNumber(item.salary.esi),
          "EPF 13%": formatNumber(item.salary.epf_employer),
          "ESI 3.25%": formatNumber(item.salary.esi_employer),
          "Gross salary": formatNumber(item.salary.actual_gross_amount),
          "Service charge": formatNumber(item.salary.service_charge),
          "Total bill amount": formatNumber(item.salary.billable_amount),
          "Net salary paid": formatNumber(item.salary.net_amount),
        }));

        const worksheet = XLSX.utils.json_to_sheet(sheetData);
        XLSX.utils.book_append_sheet(workbook, worksheet, clientGroup.client.name.slice(0, 31));
      });

      XLSX.writeFile(workbook, `Company_Salaries_${period}.xlsx`);
    } else {
      const sheetData = salaries.map((item, idx) => ({
        "Sl No.": idx + 1,
        "Employee Name": item.employee.name,
        "Employee ID": item.employee.employee_id,
        "Daily wages": formatNumber(item.salary.daily_rate),
        "No. of working days": formatNumber(item.salary.days_worked),
        "Overpayment recovery": formatNumber(0),
        "Minimum wage": formatNumber(item.salary.gross_amount),
        "EPF 12%": formatNumber(item.salary.epf),
        "ESI 0.75%": formatNumber(item.salary.esi),
        "EPF 13%": formatNumber(item.salary.epf_employer),
        "ESI 3.25%": formatNumber(item.salary.esi_employer),
        "Gross salary": formatNumber(item.salary.actual_gross_amount),
        "Service charge": formatNumber(item.salary.service_charge),
        "Total bill amount": formatNumber(item.salary.billable_amount),
        "Net salary paid": formatNumber(item.salary.net_amount),
      }));
      
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(sheetData);
      XLSX.utils.book_append_sheet(workbook, worksheet, "Salaries");
      XLSX.writeFile(workbook, `Salaries_${clientData.name}_${period}.xlsx`);
    }
  };

  const downloadPDF = () => {
    const doc = new jsPDF({
      orientation: "landscape",
      unit: "mm",
      format: "a4",
    });

    const renderHeaderSection = (clientInfo, yPosition) => {
      const pageWidth = doc.internal.pageSize.width;
      const leftMargin = 10;
      let yPos = yPosition;

      // Header section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(16);
      doc.text("FORM XVI", pageWidth / 2, yPos, { align: "center" });
      yPos += 7;

      doc.setFontSize(11);
      doc.text("See Rule 78(a)(i)", pageWidth / 2, yPos, { align: "center" });
      yPos += 7;

      doc.text("Payment Roll", pageWidth / 2, yPos, { align: "center" });
      yPos += 10;

      // Improved header details handling
      doc.setFontSize(10);
      const labelWidth = 85;
      const valueWidth = pageWidth - leftMargin - labelWidth - 25;

      const headerDetails = [
        {
          label: "Name and Address of Contractor:",
          value: company ? `${companydata?.name}, ${companydata?.address_1}` : `${clientInfo?.name}, ${clientInfo?.address}`
        },
        {
          label: "Nature and location of work:",
          value: `${clientInfo?.contract_category}, ${clientInfo?.address}`
        },
        {
          label: "Name and address of establishment in/under which contract is carried on:",
          value: clientInfo?.address
        },
        {
          label: "Name and address of Principal Employer:",
          value: company ? `${clientInfo?.name}, ${clientInfo?.address}` : `${companydata?.name}, ${companydata?.address_1}`
        },
        {
          label: "Period:",
          value: formatPeriod(period)
        }
      ];

      headerDetails.forEach(({ label, value }) => {
        doc.setFont("helvetica", "bold");
        const splitLabel = doc.splitTextToSize(label, labelWidth);
        doc.text(splitLabel, leftMargin, yPos);

        doc.setFont("helvetica", "normal");
        const splitValue = doc.splitTextToSize(value || '', valueWidth);
        doc.text(splitValue, leftMargin + labelWidth, yPos);

        const labelLines = splitLabel.length;
        const valueLines = splitValue.length;
        const lineHeight = 5;
        const maxLines = Math.max(labelLines, valueLines);
        yPos += maxLines * lineHeight + 2;
      });

      return yPos;
    };

    const renderTable = (data, startY) => {
      const headers = [
        [
          { content: 'Sl No.', rowSpan: 2 },
          { content: 'Employee Name', rowSpan: 2 },
          { content: 'Employee ID', rowSpan: 2 },
          { content: 'Daily Rate', rowSpan: 2 },
          { content: 'Allowance', rowSpan: 2 },
          { content: 'Bonus', rowSpan: 2 },
          { content: 'Days Worked', rowSpan: 2 },
          { content: 'Gross Amount', rowSpan: 2 },
          { content: 'Deductions', colSpan: 2 },
          { content: 'Employer Contribution', colSpan: 2 },
          { content: 'Net Amount', rowSpan: 2 },
          { content: 'Service Charge', rowSpan: 2 },
          { content: 'Total Bill Amount', rowSpan: 2 }
        ],
        [
          '', '', '', '', '', '',
          'EPF 12%', 'ESI 0.75%',
          'EPF 13%', 'ESI 3.25%',
          '', '', ''
        ]
      ];

      doc.autoTable({
        head: headers,
        body: data,
        startY: startY + 5,
        styles: {
          fontSize: 8,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [240, 240, 240],
          textColor: [0, 0, 0],
          fontStyle: 'bold',
          halign: 'center',
          valign: 'middle'
        },
        columnStyles: {
          0: { cellWidth: 11 },  // Sl No
          1: { cellWidth: 30 },  // Name
          2: { cellWidth: 20 },  // ID
          3: { cellWidth: 15 },  // Daily Rate
          4: { cellWidth: 15 },  // Days
          5: { cellWidth: 20 },  // Gross
          6: { cellWidth: 15 },  // EPF
          7: { cellWidth: 15 },  // ESI
          8: { cellWidth: 15 },  // EPF Employer
          9: { cellWidth: 15 },  // ESI Employer
          10: { cellWidth: 20 }, // Net Amount
          11: { cellWidth: 20 }, // Service Charge
          12: { cellWidth: 20 }  // Total Bill
        },
        theme: 'grid',
        tableWidth: 'auto',
        margin: { left: 10, right: 10 },
      });

      return doc.lastAutoTable.finalY;
    };
    const toNumber = (value) => {
      if (value === null || value === undefined) return 0; 
      const num = Number(String(value).trim());
      return Number.isFinite(num) ? num : 0; 
    };

    const calculateTotals = (salaryData) => {
  
      return  salaryData.reduce((acc, item) => ({
        daily_rate: acc.daily_rate + (item.salary.daily_rate || 0),
        allowance: acc.allowance + (toNumber(item.employee.allowance) * (item.salary.days_worked || 0)),
        bonus: acc.bonus + (toNumber(item.employee.bonus) * (item.salary.days_worked || 0)),
        days_worked: acc.days_worked + (item.salary.days_worked || 0),
        gross_amount: acc.gross_amount + (item.salary.gross_amount || 0),
        epf: acc.epf + (item.salary.epf || 0),
        esi: acc.esi + (item.salary.esi || 0),
        epf_employer: acc.epf_employer + (item.salary.epf_employer || 0),
        esi_employer: acc.esi_employer + (item.salary.esi_employer || 0),
        net_amount: acc.net_amount + (item.salary.net_amount || 0),
        service_charge: acc.service_charge + (item.salary.service_charge || 0),
        billable_amount: acc.billable_amount + (item.salary.billable_amount || 0),
      }), {
        daily_rate: 0,
        allowance:0,
        bonus:0,
        days_worked: 0,
        gross_amount: 0,
        epf: 0,
        esi: 0,
        epf_employer: 0,
        esi_employer: 0,
        net_amount: 0,
        service_charge: 0,
        billable_amount: 0,
      });
    };

    const renderTotalsRow = (totals, startY) => {
      doc.autoTable({
        body: [[
          'Total',
          '',
          '',
          formatNumber(totals.daily_rate),
          formatNumber(totals.allowance),
          formatNumber(totals.bonus),
          formatNumber(totals.days_worked),
          formatNumber(totals.gross_amount),
          formatNumber(totals.epf),
          formatNumber(totals.esi),
          formatNumber(totals.epf_employer),
          formatNumber(totals.esi_employer),
          formatNumber(totals.net_amount),
          formatNumber(totals.service_charge),
          formatNumber(totals.billable_amount)
        ]],
        startY: startY + 2,
        styles: {
          fontSize: 8,
          cellPadding: 2,
          fontStyle: 'bold',
        },
        columnStyles: {
          0: { cellWidth: 11 },
          1: { cellWidth: 30 },
          2: { cellWidth: 20 },
          3: { cellWidth: 15 },
          4: { cellWidth: 15 },
          5: { cellWidth: 20 },
          6: { cellWidth: 15 },
          7: { cellWidth: 15 },
          8: { cellWidth: 15 },
          9: { cellWidth: 15 },
          10: { cellWidth: 20 },
          11: { cellWidth: 20 },
          12: { cellWidth: 20 }
        },
        theme: 'grid',
        tableWidth: 'auto',
        margin: { left: 10, right: 10 },
      });
    };

    if (company) {
      salaries.forEach((clientGroup, index) => {
        if (index > 0) {
          doc.addPage();
        }

        let yPos = 15;
        yPos = renderHeaderSection(clientGroup.client, yPos);

        const tableData = clientGroup.employees.map((item, idx) => [
          idx + 1,
          item.employee.name,
          item.employee.employee_id,
          formatNumber(item.salary.daily_rate),
          formatNumber(item.salary.days_worked * item.employee.allowance),
          formatNumber(item.salary.days_worked * item.employee.bonus),
          formatNumber(item.salary.days_worked),
          formatNumber(item.salary.gross_amount),
          formatNumber(item.salary.epf),
          formatNumber(item.salary.esi),
          formatNumber(item.salary.epf_employer),
          formatNumber(item.salary.esi_employer),
          formatNumber(item.salary.net_amount),
          formatNumber(item.salary.service_charge),
          formatNumber(item.salary.billable_amount)
        ]);

        const finalY = renderTable(tableData, yPos);
        const totals = calculateTotals(clientGroup.employees);

        renderTotalsRow(totals, finalY);
      });
    } else {
      let yPos = 15;
      yPos = renderHeaderSection(clientData, yPos);
   

      const tableData = salaries.map((item, idx) => [
        idx + 1,
        item.employee.name,
        item.employee.employee_id,
        formatNumber(item.salary.daily_rate),
        formatNumber(item.salary.days_worked * item.employee.allowance),
        formatNumber(item.salary.days_worked * item.employee.bonus),
        formatNumber(item.salary.days_worked),
        formatNumber(item.salary.gross_amount),
        formatNumber(item.salary.epf),
        formatNumber(item.salary.esi),
        formatNumber(item.salary.epf_employer),
        formatNumber(item.salary.esi_employer),
        formatNumber(item.salary.net_amount),
        formatNumber(item.salary.service_charge),
        formatNumber(item.salary.billable_amount)
      ]);

      const finalY = renderTable(tableData, yPos);
      const totals = calculateTotals(salaries);
      renderTotalsRow(totals, finalY);
    }

    doc.save(`Salary_Report_${period}.pdf`);
  };
  
  const formatNumber = (num) => {
    if (num === null || num === undefined) return "0";
    return num.toLocaleString('en-IN', {
      maximumFractionDigits: 0,
      minimumFractionDigits: 0
    });
  };

  return (
    <>
      <header className="w-full flex items-center gap-5 mb-5">
        <h2 className="text-2xl leading-none font-semibold flex-1">
          Payment Calculator
        </h2>
        <DateFilter period={period} setPeriod={setPeriod} />
        {!company && (
          <button
            className="px-2.5 text-black bg-white border border-white rounded"
            onClick={onViewInvoice}
          >
            View Invoice
          </button>
        )}
      </header>

      {company ? (
        <div className="space-y-8">
          <div className="flex justify-end gap-2 mb-4">
            <button
              onClick={downloadPDF}
              className="px-4 py-2 text-black bg-white border border-black rounded text-sm font-medium"
            >
              Download All PDF
            </button>
            <button
              onClick={handleExport}
              className="px-4 py-2 text-black bg-white border border-black rounded text-sm font-medium"
            >
              Export All to Excel
            </button>
          </div>
          {salaries?.map((clientGroup) => (
            <section 
              key={clientGroup.client.id} 
              className="max-w-full overflow-x-auto pb-1 rounded scrollbar"
            >
              <div className="p-4 rounded-md shadow-md text-black font-bold">
                <h3 className="text-lg font-bold text-center">FORM XVI</h3>
                <p className="text-sm font-bold text-center">See Rule 78(a)(i)</p>
                <p className="text-sm font-bold text-center">Payment Roll</p>
                <p className="text-sm">
                  Name and Address of Contractor :
                  <span className="font-thin text-sm">
                    {`${companydata?.name}, ${companydata?.address_1}`}
                  </span>
                </p>
                <p className="text-sm">
                  Nature and location of work :
                  <span className="font-thin text-sm">
                    {`${clientGroup.client.contract_category}`}
                  </span>
                </p>
                <p className="text-sm">
                  Name and address of establishment in/under which contract is carried on :
                  <span className="font-thin text-sm">
                    {clientGroup.client.address}
                  </span>
                </p>
                <p className="text-sm">
                  Name and address of Principal Employer:
                  <span className="font-thin text-sm">
                    {`${clientGroup.client.name}, ${clientGroup.client.address}`}
                  </span>
                </p>
                {period && (
                  <p className="text-sm">
                    Period: {`${period.toString().slice(0,4)} ${period.toString().slice(4)}`}
                  </p>
                )}
              </div>
              <Table salaries={clientGroup.employees} />
            </section>
          ))}
        </div>
      ) : (
        // Original single client view
        salaries?.length > 0 ? (
          <section className="max-w-full overflow-x-auto pb-1 rounded scrollbar">
            <div className=" p-4 rounded-md shadow-md text-black font-bold">
              <h3 className="text-lg font-bold  text-center">FORM XVI</h3>
              <p className="text-sm font-bold text-center">See Rule 78(a)(i) </p>
              <p className="text-sm font-bold text-center">Payment Roll</p>
              <p className="text-sm">
                Name and Address of Contractor :
                <span className="font-thin text-sm">
                {companydata?.name},{" "}
                {companydata?.address_1}
                </span>
              </p>
              <p className="text-sm">
                Nature and location of work :
                <span className="font-thin text-sm">
                {clientData?.contract_category}
     
                </span>
                
              </p>
              <p className="text-sm">
                Name and address of establishment in/under which contract is carried
                on :
                <span className="font-thin text-sm">
                {clientData?.address}
                </span>
                
              </p>
              <p className="text-sm">
                Name and address of Principal Employer:
                <span className="font-thin text-sm">
                {clientData.name},{clientData.address}
                </span>
                 
              </p>
              {period && 
              <p className="text-sm">Period: {`${period.toString().slice(0,4)} ${period.toString().slice(4)}`}</p>

              }
            </div>
              <Table salaries={salaries} />
            </section>
          ) : (
            <div className="bg-gray-100 p-5 text-gray-600 text-center rounded">
              No data for the selected period
            </div>
          )
        )}
    </>
  );
};

export default All;
