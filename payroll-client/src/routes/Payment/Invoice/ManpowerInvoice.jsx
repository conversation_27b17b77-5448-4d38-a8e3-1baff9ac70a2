import { formatPeriod } from "../../../utils/utils";
import { getStringFromPeriod } from "./PDF-Generator";
import { Phone, Mail, MapPin } from "lucide-react";
import IFMLOGO from "../../../ifm.png";
import IFMFOOTER from "../../../letter-head-footer.png";

const ManpowerInvoice = ({ data, logourl, companyData, clientData }) => {
  const selectedComapny = JSON.parse(localStorage.getItem("selected-company"));
  const selectedClient = JSON.parse(localStorage.getItem("selected-client"));
  const convertNumberToWords = (num) => {
    if (isNaN(num)) return "Zero";

    const ones = [
      "",
      "One",
      "Two",
      "Three",
      "Four",
      "Five",
      "Six",
      "Seven",
      "Eight",
      "Nine",
      "Ten",
      "Eleven",
      "Twelve",
      "Thirteen",
      "Fourteen",
      "Fifteen",
      "Sixteen",
      "Seventeen",
      "Eighteen",
      "Nineteen",
    ];
    const tens = [
      "",
      "",
      "Twenty",
      "Thirty",
      "Forty",
      "Fifty",
      "Sixty",
      "Seventy",
      "Eighty",
      "Ninety",
    ];
    const thousands = ["", "Thousand", "Million", "Billion"];

    if (num === 0) return "Zero";

    let word = "";
    let i = 0;

    while (num > 0) {
      if (num % 1000 !== 0) {
        word = `${convertHundreds(num % 1000)} ${thousands[i]} ${word}`;
      }
      num = Math.floor(num / 1000);
      i++;
    }

    return word.trim();
  };

  const convertHundreds = (num) => {
    const ones = [
      "",
      "One",
      "Two",
      "Three",
      "Four",
      "Five",
      "Six",
      "Seven",
      "Eight",
      "Nine",
      "Ten",
      "Eleven",
      "Twelve",
      "Thirteen",
      "Fourteen",
      "Fifteen",
      "Sixteen",
      "Seventeen",
      "Eighteen",
      "Nineteen",
    ];
    const tens = [
      "",
      "",
      "Twenty",
      "Thirty",
      "Forty",
      "Fifty",
      "Sixty",
      "Seventy",
      "Eighty",
      "Ninety",
    ];

    if (num === 0) return "";

    if (num < 20) return ones[num];
    if (num < 100) return `${tens[Math.floor(num / 10)]} ${ones[num % 10]}`;
    return `${ones[Math.floor(num / 100)]} Hundred ${convertHundreds(
      num % 100
    )}`;
  };

  const safeGetNumber = (value) => {
    return isNaN(value) ? 0 : Number(value);
  };
  const netReceivable = safeGetNumber(data.details?.net_receivable);
  const getFormattedDate = () => {
    const date = new Date();
    const day = date.getDate();
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    return `${day} ${month} ${year}`;
  };

  return (
    <div
      id="invoice-template"
      className="mx-auto my-0  pt-5 pl-5 pr-5 pb-1 text-sm font-sans bg-white    border-black "
      style={{ width: "210mm", minHeight: "297mm" }} // A4 size
    >
      <div className="flex justify-between mb-5">
        {selectedComapny.name === "Image Facility Management PVT LTD" ? (
          <>
            <div>
              <img src={logourl} alt="Logo" className="h-48 w-48" />
            </div>
            <div className="flex flex-col gap-1 pt-12">
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-green-600" />
                <span className="text-[10px] text-green-600">
                  +91 {selectedComapny.contact_number}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-green-600" />
                <div className="flex flex-col">
                  <span className="text-[10px] text-green-600">
                    {selectedComapny.email}
                  </span>
                  <span className="text-[10px] text-green-600">
                    {selectedComapny.secondary_email}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-green-600" />
                <div className="flex flex-col">
                  <span className="text-[10px] text-green-600">
                    {selectedComapny.address_1}
                  </span>
                  <span className="text-[10px] text-green-600">
                    {selectedComapny.branch}, {selectedComapny.district} -
                    {selectedComapny.pin}
                  </span>
                </div>
              </div>
            </div>
          </>
        ) : companyData.name === "IFM INDIA" ? (
          <div className="h-[230px] w-[753px]">
            <img src={IFMLOGO} alt="" />
          </div>
        ) : (
          <div
            className="h-[280px] w-[753px]"
            style={{
              backgroundImage: `url(${BIVILOGO})`,
              backgroundSize: "cover",
              backgroundPosition: `0px -100px`,
            }}
          ></div>
        )}
      </div>

      {/* INVOICE Title */}
      <div
        className={`text-center font-bold my-5 text-base ${
          selectedComapny.name !== "Image Facility Management PVT LTD"
            ? "-mt-32"
            : ""
        } `}
      >
        INVOICE
      </div>

      {/* Party Details */}
      <div className="border border-black p-4">
        <div className="flex justify-between">
          {/* Left Column */}
          <div className="flex-1">
            <div className="flex">
              <div className="w-[90px]">Party Name</div>
              <div className="w-[15px] text-center">:</div>
              <div className="flex-1">
                {selectedClient?.name || "THE DIRECTOR"}
              </div>
            </div>

            <div className="flex">
              <div className="w-[90px] self-start">Address</div>
              <div className="w-[15px] text-center">:</div>
              <div className="flex-1">
                {selectedClient?.address}
                <br />
              </div>
            </div>

            <div className="flex mt-0">
              <div className="w-[90px]">Contract no.</div>
              <div className="w-[15px] text-center">:</div>
              <div className="flex-1">{clientData.contract_number || ""}</div>
            </div>
          </div>

          {/* Right Column */}
          <div>
            <div>
              <table className="text-left">
                <tbody>
                  <tr>
                    <td className="w-[90px]">Invoice No</td>
                    <td className="px-1">:</td>

                    <td>{`242/IFMPVT/${data?.period}`}</td>
                  </tr>
                  <tr>
                    <td className="w-[90px]">Invoice Date</td>
                    <td className="px-1">:</td>
                    <td>{getFormattedDate()}</td>
                  </tr>
                  <tr>
                    <td className="w-[90px]">GST NO</td>
                    <td className="px-1">:</td>
                    <td className="px-1">{selectedClient.gst || ""}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Main Table */}
      <table className="w-full border-collapse border border-gray-900 text-xs mb-2">
        <thead>
          <tr>
            <th className="border border-gray-900 p-1 h-7">
              Particulars/ service description
            </th>
            <th className="border border-gray-900 p-1">HSN/SAC</th>
            <th className="border border-gray-900 p-1">No. of staff</th>
            <th className="border border-gray-900 p-1">No. of duty</th>
            <th className="border border-gray-900 p-1">Rate</th>
            <th className="border border-gray-900 p-1">Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td
              className="border border-gray-900 p-2 font-semibold"
              colSpan={6}
            >
              Manpower Outsourcing Services - Job Contract for the Month of {formatPeriod(data?.period)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2">Basic Pay</td>
            <td className="border border-gray-900 p-2 text-center">
              {data.basic_pay}
            </td>
            <td className="border border-gray-900 p-2 text-center">
              {safeGetNumber(data?.details?.staff_count)}
            </td>
            <td className="border border-gray-900 p-2 text-center">
              {safeGetNumber(data?.details?.duty_count)}
            </td>
            <td className="border border-gray-900 p-2 text-center">
              {safeGetNumber(data?.details?.rate)}
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.basic_pay).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2">EPF @12%</td>
            <td className="border border-gray-900 p-2 text-center" colSpan={4}>
              Restricted to Basic Pay Rs.15000/- (ie Rs.1800/- per person,
              Rs.15000*12%)
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.epf).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2">ESI @ 3.25%</td>
            <td
              className="border border-gray-900 p-2 text-center"
              colSpan={4}
            ></td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.esi).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2">Allowance</td>
            <td
              className="border border-gray-900 p-2 text-center"
              colSpan={4}
            ></td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.allowance).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2">Bonus</td>
            <td
              className="border border-gray-900 p-2 text-center"
              colSpan={4}
            ></td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.bonus).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2">EDLI @0.5%</td>
            <td className="border border-gray-900 p-2 text-center" colSpan={4}>
              Restricted to Basic Pay Rs.15000/-. (ie Rs.75/- per person,
              Rs.15000*0.5%)
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.edli).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2">
              PF Admin. Charges @ 0.5%
            </td>
            <td className="border border-gray-900 p-2 text-center" colSpan={4}>
              Restricted to Basic Pay Rs.15000/- (ie Rs.75/- per person,
              Rs.15000*0.5%)
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.pf_admin_charges).toFixed(2)}
            </td>
          </tr>
          <tr className="font-semibold">
            <td className="border border-gray-900 p-2 text-right" colSpan={5}>
              Total wages
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.total_wages).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2">
              Service Charge @3.26% (without GST)
            </td>
            <td className="border border-gray-900 p-2" colSpan={4}></td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.service_charge).toFixed(2)}
            </td>
          </tr>
          <tr className="font-semibold">
            <td className="border border-gray-900 p-2 text-right" colSpan={5}>
              TOTAL WAGES PER MONTH
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.total_amount).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2 text-right" colSpan={5}>
              CGST 9%
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.cgst).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td className="border border-gray-900 p-2 text-right" colSpan={5}>
              SGST 9%
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.sgst).toFixed(2)}
            </td>
          </tr>
          <tr className="font-semibold">
            <td className="border border-gray-900 p-2 text-right" colSpan={5}>
              Total
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {safeGetNumber(data?.details?.net_receivable).toFixed(2)}
            </td>
          </tr>
          <tr>
            <td
              className="border border-gray-900 p-2 text-right italic"
              colSpan={5}
            >
              Round Off
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {(Math.round(netReceivable) - netReceivable).toFixed(2)}
            </td>
          </tr>
          <tr className="font-semibold">
            <td className="border border-gray-900 p-2 text-right" colSpan={5}>
              Net Amount Receivable
            </td>
            <td className="border border-gray-900 p-2 text-right">
              {Math.round(data?.details?.net_receivable)}
            </td>
          </tr>
        </tbody>
      </table>

      {/* Amount in Words */}
      <div className="my-5 text-sm">
        Amount in Words:{" "}
        <span className="font-semibold">
          Rupees {convertNumberToWords(Math.round(netReceivable))}
        </span>
      </div>

      {/* Payment Details */}
      <div className="mt-5 text-sm">
        <p className=" italic text-black font-medium border "></p>
        <p className="italic text-black font-bold border p-2 border-gray-900">
          Payment Cheque /NEFT/RTGS in the name of "{companyData.name}"
        </p>
        <table className="w-full border border-gray-900 text-xs">
          <tbody>
            <tr>
              <td className="p-2 font-semibold w-20">Bank</td>
              <td className="p-2">
                {companyData?.bank_name?.toUpperCase() || ""}
              </td>
            </tr>
            <tr>
              <td className="p-2 font-semibold">A/C No</td>
              <td className="p-2">{companyData?.bank_account_number || ""}</td>
            </tr>
            <tr>
              <td className="p-2 font-semibold">Branch</td>
              <td className="p-2">
                {companyData?.branch?.toUpperCase() || ""}
              </td>
            </tr>
            <tr>
              <td className="p-2 font-semibold">IFSC</td>
              <td className="p-2">
                {companyData?.ifsc_number ? `${companyData?.ifsc_number}` : ""}
              </td>
            </tr>
            {companyData?.gstin && companyData?.gstin !== "" && (
              <tr>
                <td className="p-2 font-semibold">GSTIN</td>
                <td className="p-2">{companyData?.gstin || ""}</td>
              </tr>
            )}

            <tr>
              <td className="p-2 font-semibold">PAN</td>
              <td className="p-2">{companyData?.pan?.toUpperCase() || ""}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="flex justify-between mt-1 text-center text-sm">
        <div>Prepared by</div>
        <div>Authorized By</div>
      </div>

      <div className="text-center  italic text-sm ">*Thanking You*</div>

      {companyData.name === "BIVI" && (
        <div className="flex justify-evenly mt-2">
          <div>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100">
                <MapPin className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex flex-col">
                <span className="text-[10px] text-green-600">
                  {companyData.address_1}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100">
              <Phone className="w-4 h-4 text-green-600" />
            </div>
            <span className="text-[10px] text-green-600">
              +91 {companyData.contact_number}
            </span>
          </div>

          <div>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100">
                <Mail className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex flex-col">
                <span className="text-[10px] text-green-600">
                  {companyData.email}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
      {companyData.name === "IFM INDIA" && (
        <div
          style={{
            width: "210mm",
            height: "50px",
            backgroundColor: "#4d0acf",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            color: "white",
            fontWeight: "bold",
          }}
          className="mt-3 -mb-1 h-full -ml-5 "
        >
          {" "}
          "An ISO 9001-2015 Certified Company"{" "}
        </div>
      )}
    </div>
  );
};

export default ManpowerInvoice;
