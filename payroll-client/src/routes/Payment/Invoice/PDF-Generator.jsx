import React from "react";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import ManpowerInvoice from "./ManpowerInvoice";
import { Mail, MapPin, Phone } from "lucide-react";
import { formatPeriod } from "../../../utils/utils";
import <PERSON><PERSON>OG<PERSON> from "../../../ifm.png";
import BIVILOG<PERSON> from "../../../BIVI.INVOICE___LETTER_HEAD-1-removebg.png";
import IFMFOOTER from "../../../letter-head-footer.png";

export const getStringFromPeriod = (period) => {
  return period || "November 2024";
};

const InvoiceTemplate = ({ data, logoUrl }) => {
  const companyData = JSON.parse(localStorage.getItem("selected-company"));
  const clientData = JSON.parse(localStorage.getItem("selected-client"));

  const getFormattedDate = () => {
    const date = new Date();
    const day = date.getDate();
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    return `${day} ${month} ${year}`;
  };

  const convertNumberToWords = (num) => {
    if (isNaN(num)) return "Zero";

    const ones = [
      "",
      "One",
      "Two",
      "Three",
      "Four",
      "Five",
      "Six",
      "Seven",
      "Eight",
      "Nine",
      "Ten",
      "Eleven",
      "Twelve",
      "Thirteen",
      "Fourteen",
      "Fifteen",
      "Sixteen",
      "Seventeen",
      "Eighteen",
      "Nineteen",
    ];
    const tens = [
      "",
      "",
      "Twenty",
      "Thirty",
      "Forty",
      "Fifty",
      "Sixty",
      "Seventy",
      "Eighty",
      "Ninety",
    ];
    const thousands = ["", "Thousand", "Million", "Billion"];

    if (num === 0) return "Zero";

    let word = "";
    let i = 0;

    while (num > 0) {
      if (num % 1000 !== 0) {
        word = `${convertHundreds(num % 1000)} ${thousands[i]} ${word}`;
      }
      num = Math.floor(num / 1000);
      i++;
    }

    return word.trim();
  };

  const convertHundreds = (num) => {
    const ones = [
      "",
      "One",
      "Two",
      "Three",
      "Four",
      "Five",
      "Six",
      "Seven",
      "Eight",
      "Nine",
      "Ten",
      "Eleven",
      "Twelve",
      "Thirteen",
      "Fourteen",
      "Fifteen",
      "Sixteen",
      "Seventeen",
      "Eighteen",
      "Nineteen",
    ];
    const tens = [
      "",
      "",
      "Twenty",
      "Thirty",
      "Forty",
      "Fifty",
      "Sixty",
      "Seventy",
      "Eighty",
      "Ninety",
    ];

    if (num === 0) return "";

    if (num < 20) return ones[num];
    if (num < 100) return `${tens[Math.floor(num / 10)]} ${ones[num % 10]}`;
    return `${ones[Math.floor(num / 100)]} Hundred ${convertHundreds(
      num % 100
    )}`;
  };

  const safeGetNumber = (value) => {
    return isNaN(value) ? 0 : Number(value);
  };
  const netReceivable = safeGetNumber(data.details?.net_receivable);

  return (
    <>
      {data.client?.contract_category?.toLowerCase() ===
      `manpower outsourcing` ? (
        <ManpowerInvoice
          data={data}
          logourl={logoUrl}
          companyData={companyData}
          clientData={clientData}
        />
      ) : (
        <div
          id="invoice-template"
          className="mx-auto my-0  pt-5 pl-5 pr-5 pb-1 text-sm font-sans bg-white hidden   border-black "
          style={{ width: "210mm", minHeight: "297mm" }}
        >
          <div className="flex justify-between mb-5">
            {companyData.name === "Image Facility Management PVT LTD" ? (
              <>
                <div>
                  <img src={logoUrl} alt="Logo" className="h-48 w-48" />
                </div>
                <div className="flex flex-col gap-1 pt-12">
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-green-600" />
                    <span className="text-[10px] text-green-600">
                      +91 {companyData.contact_number}
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-green-600" />
                    <div className="flex flex-col">
                      <span className="text-[10px] text-green-600">
                        {companyData.email}
                      </span>
                      <span className="text-[10px] text-green-600">
                        {companyData.secondary_email}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-green-600" />
                    <div className="flex flex-col">
                      <span className="text-[10px] text-green-600">
                        {companyData.address_1}
                      </span>
                      <span className="text-[10px] text-green-600">
                        {companyData.branch}, {companyData.district} -
                        {companyData.pin}
                      </span>
                    </div>
                  </div>
                </div>
              </>
            ) : companyData.name === "IFM INDIA" ? (
              <div className="h-[280px] w-[753px]">
                <img src={IFMLOGO} alt="" />
              </div>
            ) : (
              <div
                className="h-[280px] w-[753px]"
                style={{
                  backgroundImage: `url(${BIVILOGO})`,
                  backgroundSize: "cover",
                  backgroundPosition: `0px -100px`,
                }}
              ></div>
            )}
          </div>

          {/* INVOICE Title */}
          <div
            className={`text-center font-bold my-5 text-base ${
              companyData.name !== "Image Facility Management PVT LTD"
                ? "-mt-32"
                : ""
            } `}
          >
            INVOICE
          </div>

          <div className="border border-black p-4">
            <div className="flex justify-between">
              {/* Left Column */}
              <div className="flex-1">
                <div className="flex">
                  <div className="w-[90px]">Party Name</div>
                  <div className="w-[15px] text-center">:</div>
                  <div className="flex-1">
                    {clientData?.name || "THE DIRECTOR"}
                  </div>
                </div>

                <div className="flex">
                  <div className="w-[90px] self-start">Address</div>
                  <div className="w-[15px] text-center">:</div>
                  <div className="flex-1">
                    {clientData?.address}
                    <br />
                  </div>
                </div>

                <div className="flex mt-0">
                  <div className="w-[90px]">Contract no.</div>
                  <div className="w-[15px] text-center">:</div>
                  <div className="flex-1">
                    {clientData.contract_number || ""}
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div>
                <div>
                  <table className="text-left">
                    <tbody>
                      <tr>
                        <td className="w-[90px]">Invoice No</td>
                        <td className="px-1">:</td>
                        <td>{`242/IFMPVT/${data?.period}`}</td>
                      </tr>
                      <tr>
                        <td className="w-[90px]">Invoice Date</td>
                        <td className="px-1">:</td>
                        <td>{getFormattedDate()}</td>
                      </tr>
                      <tr>
                    <td className="w-[90px]">GST NO</td>
                    <td className="px-1">:</td>
                    <td className="px-1">{clientData.gst || ""}</td>
                  </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <table className="w-full mb-4 border-collapse">
            <thead>
              <tr className="border border-gray-900">
                <th className="text-left p-2 w-3/4 border border-gray-900">
                  Description
                </th>
                <th className="text-right p-2 border border-gray-900">
                  Amount
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="h-6 border border-gray-900">
                <td className="border border-gray-900"></td>
                <td className="border border-gray-900"></td>
              </tr>
              <tr className="border border-gray-900">
                <td className="p-2 border border-gray-900 ">
                  House keeping charge for the Month of{" "}
                  {formatPeriod(data.period)}
                </td>
                <td className="p-1 text-right border border-gray-900">
                  {Math.round(data.details.total_wages)}
                </td>
              </tr>
              {[...Array(5)].map((_, i) => (
                <tr key={i} className="h-6 border border-gray-900">
                  <td className="border border-gray-900"></td>
                  <td className="border border-gray-900"></td>
                </tr>
              ))}
              <tr className="border border-gray-900">
                <td className="p-2 font-semibold border border-gray-900">
                  Taxable Value
                </td>
                <td className="p-2 text-right font-semibold border border-gray-900">
                  {Math.round(data.details.total_wages)}
                </td>
              </tr>
              {[...Array(2)].map((_, i) => (
                <tr key={i} className="h-6 border border-gray-900">
                  <td className="border border-gray-900"></td>
                  <td className="border border-gray-900"></td>
                </tr>
              ))}
              <tr className="border border-gray-900">
                <td className="p-2 font-semibold border border-gray-900">
                  CGST 9%
                </td>
                <td className="p-2 text-right font-semibold border border-gray-900">
                  {Math.round(data.details.cgst)}
                </td>
              </tr>
              <tr className="h-6 border border-gray-900">
                <td className="border border-gray-900"></td>
                <td className="border border-gray-900"></td>
              </tr>
              <tr className="border border-gray-900">
                <td className="p-2 font-semibold border border-gray-900">
                  SGST 9%
                </td>
                <td className="p-2 text-right font-semibold border border-gray-900">
                  {Math.round(data.details.sgst)}
                </td>
              </tr>
              {[...Array(2)].map((_, i) => (
                <tr key={i} className="h-6 border border-gray-900">
                  <td className="border border-gray-900"></td>
                  <td className="border border-gray-900"></td>
                </tr>
              ))}
              <tr className="border border-gray-900">
                <td className="p-2 font-semibold border border-gray-900">
                  Net Amount Receivable
                </td>
                <td className="p-2 text-right font-semibold border border-gray-900">
                  {" "}
                  {Math.round(netReceivable)}
                </td>
              </tr>
            </tbody>
          </table>

          <div className="my-5 text-sm">
            Amount in Words:{" "}
            <span className="font-semibold">
              Rupees {convertNumberToWords(Math.round(netReceivable))}
            </span>
          </div>

          <div className="mt-5 text-sm">
            <p className=" italic text-black font-medium border border-gray-900 p-2">
              SAC CODE :9885
            </p>
            <p className="italic text-black font-bold border border-gray-900 p-3">
              Payment Cheque /NEFT/RTGS in the name of "{companyData.name}"
            </p>
            <table className="w-full border-collapse border border-gray-900 text-xs">
              <tbody>
                <tr>
                  <td className="border border-gray-900 p-2 font-semibold w-20">
                    Bank
                  </td>
                  <td className="border border-gray-900 p-2">
                    {companyData?.bank_name?.toUpperCase() || ""}
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-900 p-2 font-semibold">
                    A/C No
                  </td>
                  <td className="border border-gray-900 p-2">
                    {companyData?.bank_account_number || ""}
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-900 p-2 font-semibold">
                    Branch
                  </td>
                  <td className="border border-gray-900 p-2">
                    {companyData?.branch?.toUpperCase() || ""}
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-900 p-2 font-semibold">
                    IFSC
                  </td>
                  <td className="border border-gray-900 p-2">
                    {companyData?.ifsc_number
                      ? `${companyData?.ifsc_number}`
                      : ""}
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-900 p-2 font-semibold">
                    GSTIN
                  </td>
                  <td className="border border-gray-900 p-2">
                    {companyData?.gstin || ""}
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-900 p-2 font-semibold">
                    PAN
                  </td>
                  <td className="border border-gray-900 p-2">
                    {companyData?.pan?.toUpperCase() || ""}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className="flex justify-between mt-3 text-center text-sm">
            <div>Prepared by</div>
            <div>Authorized By</div>
          </div>

          <div className="text-center mt- italic text-sm mb-3">*Thanking You*</div>

          {companyData.name === "IFM INDIA" && (
                 <div 
                 style={{
                   width: "100%", 
                   height: "50px", 
                   backgroundColor: "#4d0acf", 
                   display: "flex", 
                   justifyContent: "center", 
                   alignItems: "center", 
                   color: "white", 
                   fontWeight: "bold"
                 }}
               >
                 "An ISO 9001-2015 Certified Company"
               </div>
                )}
          {companyData.name === "BIVI" && (
            <div className="flex justify-evenly mt-2">
              <div>
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100">
                    <MapPin className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-[10px] text-green-600">
                      {companyData.address_1}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100">
                  <Phone className="w-4 h-4 text-green-600" />
                </div>
                <span className="text-[10px] text-green-600">
                  +91 {companyData.contact_number}
                </span>
              </div>

              <div>
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100">
                    <Mail className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-[10px] text-green-600">
                      {companyData.email}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
};

const PDFGenerator = ({ invoice, logoUrl }) => {
  const generatePDF = async () => {
    try {
      const element = document.getElementById("invoice-template");
      if (!element) return;

      // Remove hidden class temporarily
      element.classList.remove("hidden");

      // Wait for fonts to load
      await document.fonts?.ready;

      // Get A4 dimensions in pixels (assuming 96 DPI)
      const a4Width = 210; // mm
      const a4Height = 297; // mm
      const pixelsPerMm = 96 / 25.4; // 96 DPI to mm conversion

      // Calculate maximum dimensions that will fit on one page with margins
      const maxWidth = (a4Width ) * pixelsPerMm; // 20mm total margins
      const maxHeight = (a4Height ) * pixelsPerMm; // 20mm total margins

      // Create canvas with optimized scale
      const canvas = await html2canvas(element, {
        scale: 2, // Keep high resolution for quality
        useCORS: true,
        logging: false,
        backgroundColor: "#ffffff",
      });

      // Hide element again
      element.classList.add("hidden");

      // Calculate scaling factors
      const scaleWidth = maxWidth / canvas.width;
      const scaleHeight = maxHeight / canvas.height;
      const scale = Math.min(scaleWidth, scaleHeight, 1); // Don't scale up, only down if needed

      // Create PDF with A4 dimensions
      const pdf = new jsPDF("p", "mm", "a4");

      // Convert canvas to image
      const imgData = canvas.toDataURL("image/png", 1.0);

      // Calculate dimensions for PDF
      const pdfWidth = (canvas.width * scale) / pixelsPerMm;
      const pdfHeight = (canvas.height * scale) / pixelsPerMm;

      // Center the content on the page
      const x = (a4Width - pdfWidth) / 2;
      const y = (a4Height - pdfHeight) / 2;

      // Add image to PDF
      pdf.addImage(imgData, "PNG", x, y, pdfWidth, pdfHeight);

      // Save PDF
      pdf.save(`invoice-${invoice?.period}-${invoice?.client?.id}.pdf`);
    } catch (error) {
      console.error("PDF generation error:", error);
      alert("Error generating PDF. Please try again.");
    }
  };

  return (
    <>
      <button
        className="px-2.5 text-white border border-white rounded hover:bg-green-600"
        onClick={generatePDF}
      >
        Generate PDF
      </button>

      <InvoiceTemplate data={invoice} logoUrl={logoUrl} />
    </>
  );
};

export default PDFGenerator;
