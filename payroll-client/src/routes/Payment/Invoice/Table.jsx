/* eslint-disable react/prop-types */

import { getStringFromPeriod } from "../../../utils/datetime";

const Table = ({ data }) => {
  return (
    <table className="bg-white w-full rounded overflow-hidden">
      <tbody>
        <tr>
          <td className="px-2.5 py-1.5 border font-medium">
            Particulars / Service description
          </td>
          <td className="px-2.5 py-1.5 border font-medium">HSN / SAC</td>
          <td className="px-2.5 py-1.5 border font-medium">No. of staff</td>
          <td className="px-2.5 py-1.5 border font-medium">No. of duty</td>
          <td className="px-2.5 py-1.5 border font-medium">Rate</td>
          <td className="px-2.5 py-1.5 border font-medium">Amount</td>
        </tr>
        <tr>
          <td className="px-2.5 py-1.5 border font-medium" colSpan="6">
            {data.client.contract_category}, for the month{" "}
            {getStringFromPeriod(data.period)}
          </td>
        </tr>
        <tr>
          <td className="px-2.5 py-1.5 border">Basic Pay</td>
          <td className="px-2.5 py-1.5 border">{/* TODO: HSN / SAC */}</td>
          <td className="px-2.5 py-1.5 border">{data.details.staff_count}</td>
          <td className="px-2.5 py-1.5 border">{data.details.duty_count}</td>
          <td className="px-2.5 py-1.5 border">{data.details.rate}</td>
          <td className="px-2.5 py-1.5 border">{Number(data.details.basic_pay)
    ? Number(data.details.basic_pay).toFixed(2)
    : data.details.basic_pay}</td>
        </tr>
        <tr>
  <td className="px-2.5 py-1.5 border">EPF @ 12%</td>
  <td className="px-2.5 py-1.5 border" colSpan="4">
    Restricted to basic pay Rs.15,000/- (ie, Rs.1800 per person)
  </td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.epf) ? Number(data.details.epf).toFixed(2) : data.details.epf}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border">ESI @ 3.25%</td>
  <td className="px-2.5 py-1.5 border" colSpan="4"></td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.esi) ? Number(data.details.esi).toFixed(2) :data.details.esi}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border">EDLI @ 0.5%</td>
  <td className="px-2.5 py-1.5 border" colSpan="4">
    Restricted to basic pay Rs.15,000/- (ie, Rs.75 per person)
  </td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.edli) ? Number(data.details.edli).toFixed(2) : data.details.edli}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border">PF Admin Charges @ 0.5%</td>
  <td className="px-2.5 py-1.5 border" colSpan="4">
    Restricted to basic pay Rs.15,000/- (ie, Rs.75 per person)
  </td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.pf_admin_charges)
      ? Number(data.details.pf_admin_charges).toFixed(2)
      : data.details.pf_admin_charges}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border">
    Allowance
  </td>
  <td className="px-2.5 py-1.5 border" colSpan="4"></td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.allowance)
      ? Number(data.details.allowance).toFixed(2)
      : data.details.allowance}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border">
    Bonus
  </td>
  <td className="px-2.5 py-1.5 border" colSpan="4"></td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.bonus)
      ? Number(data.details.bonus).toFixed(2)
      : data.details.bonus}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border">Total wages</td>
  <td className="px-2.5 py-1.5 border" colSpan="4"></td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.total_wages)
      ? Number(data.details.total_wages).toFixed(2)
      : data.details.total_wages}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border">
    Service Charge @ 3.26% (without GST)
  </td>
  <td className="px-2.5 py-1.5 border" colSpan="4"></td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.service_charge)
      ? Number(data.details.service_charge).toFixed(2)
      : data.details.service_charge}
  </td>
</tr>

<tr>
  <td className="px-2.5 py-1.5 border" colSpan="5">
    Total wages per month
  </td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.total_amount)
      ? Number(data.details.total_amount).toFixed(2)
      : data.details.total_amount}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border" colSpan="5">
    CGST @ 9%
  </td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.cgst) ? Number(data.details.cgst).toFixed(2) : data.details.cgst}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border" colSpan="5">
    SGST @ 9%
  </td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.sgst) ? Number(data.details.sgst).toFixed(2) : data.details.sgst}
  </td>
</tr>
<tr>
  <td className="px-2.5 py-1.5 border" colSpan="5">
    Net Amount Receivable
  </td>
  <td className="px-2.5 py-1.5 border">
    {Number(data.details.net_receivable)
      ? Number(data.details.net_receivable).toFixed(2)
      : data.details.net_receivable}
  </td>
</tr>

      </tbody>
    </table>
  );
};

export default Table;
