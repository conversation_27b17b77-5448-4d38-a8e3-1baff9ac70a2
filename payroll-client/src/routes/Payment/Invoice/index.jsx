import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import salariesApi from "../../../apis/salaries";
import PageLoader from "../../../components/PageLoader";
import Error from "../../../components/Error";
import Table from "./Table";
import PDFGenerator from './PDF-Generator';

const Invoice = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [invoice, setInvoice] = useState();
  const company = JSON.parse(localStorage.getItem("selected-company"))
  const logoUrl = company.logo_url
  const { period, clientId } = useParams();

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const params = { period, clientId };
      const { data } = await salariesApi.fetchInvoice(params);
      setInvoice(data.invoice);
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [period]);

  if (isLoading) {
    return <PageLoader />;
  }

  if (!invoice) {
    return <Error title="Could not load invoice data" />;
  }

  return (
    <>
      <header className="w-full flex items-center gap-5 mb-5">
        <h2 className="text-2xl leading-none font-semibold flex-1">Invoice</h2>
        {invoice && <PDFGenerator invoice={invoice}  logoUrl={logoUrl}  />}
      </header>
      <Table data={invoice} />
    </>
  );
};

export default Invoice;
