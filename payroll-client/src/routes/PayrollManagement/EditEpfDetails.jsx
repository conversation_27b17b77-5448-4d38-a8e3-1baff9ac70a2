import React, { useState, useEffect } from "react";

const EditEpfModal = ({ isOpen, onClose, selectedItem, onSave }) => {
  const [epsWage, setepsWage] = useState(selectedItem?.gross_amount || "");
  const [ncpDays, setncpDays] = useState(
    selectedItem?.attendance || ""
  );

  useEffect(() => {
    if (selectedItem) {
      setepsWage(selectedItem.gross_amount);
      setncpDays(selectedItem.attendance);
    }
  }, [selectedItem]);

  const handleSave = () => {
    onSave({ ...selectedItem, gross_amount: epsWage, attendance: ncpDays });
    onClose(); 
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white p-6 rounded shadow-lg w-96">
        <h2 className="text-xl mb-4">Edit ESI Details</h2>
        <div className="mb-4">
          <label className="block text-sm font-medium">EPS Wage</label>
          <input
            type="text"
            value={epsWage}
            onChange={(e) => setepsWage(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded mt-1"
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium">NCP Days</label>
          <input
            type="text"
            value={ncpDays}
            onChange={(e) => setncpDays(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded mt-1"
          />
        </div>
        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-white rounded"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-green-600 text-white rounded"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditEpfModal;
