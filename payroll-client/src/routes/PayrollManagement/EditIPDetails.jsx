import React, { useState, useEffect } from "react";

const EditIPDetails = ({ isOpen, onClose, selectedItem, onSave }) => {
  const [reasonCode, setReasonCode] = useState(selectedItem?.reason_code || "");
  const [lastWorkingDay, setLastWorkingDay] = useState(
    selectedItem?.last_working_day || ""
  );

  useEffect(() => {
    if (selectedItem) {
      setReasonCode(selectedItem.reason_code);
      setLastWorkingDay(selectedItem.last_working_day);
    }
  }, [selectedItem]);

  const handleSave = () => {
    onSave({ ...selectedItem, reason_code: reasonCode, last_working_day: lastWorkingDay });
    onClose(); 
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white p-6 rounded shadow-lg w-96">
        <h2 className="text-xl mb-4">Edit ESI Details</h2>
        <div className="mb-4">
          <label className="block text-sm font-medium">Reason Code</label>
          <input
            type="text"
            value={reasonCode}
            onChange={(e) => setReasonCode(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded mt-1"
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium">Last Working Day</label>
          <input
            type="date"
            value={lastWorkingDay}
            onChange={(e) => setLastWorkingDay(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded mt-1"
          />
        </div>
        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-white rounded"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-green-600 text-white rounded"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditIPDetails;
