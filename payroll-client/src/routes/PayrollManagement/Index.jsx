import { Route, Routes, useParams } from "react-router-dom";
import PayrollManagement from "./Table";
import Sidebar from "../../components/Sidebar";
import EPFDetails from "../Epf-details/EpfDetails";
import IPDetails from "../IP-details/IPDetails";
import SalaryAll from "../Salary/All";
import PaymentAll from "../Payment/All";
import AttendanceAll from "../Attendance/All";
import CompanyWageSlips from "../Salary/WageSlip/company-index";
const Employee = () => {

    const {companyId} = useParams()
    const MAIN_LINKS = [
      { label: "All Clients", route: `/${companyId}/clients` },
      { label: "New Client", route: `/${companyId}/clients/new` },
      {
        label: "Reports",
        subLinks: [
          { label: "Payroll", route: `/${companyId}/payroll` },
          { label: "EPF Details", route: `/${companyId}/payroll/epf-details` },
          { label: "ESI Details", route: `/${companyId}/payroll/ip-details` },
          { label: "Attendance", route: `/${companyId}/payroll/attendance` },
          { label: "Register of Wages", route: `/${companyId}/payroll/wage-register` },
          { label: "Payments", route: `/${companyId}/payroll/payments` },
      { label: "Wage Slip", route: `/${companyId}/payroll/company-wage-slips` },

        ],
      },
    ];
    
  return (
    <div className="w-screen h-screen flex items-stretch">
            <Sidebar mainLinks={MAIN_LINKS} />

      <main className="flex-1 bg-green-500 p-10 overflow-auto">
        <Routes>
          <Route path="" element={<PayrollManagement />} />
          <Route path="/epf-details" element={<EPFDetails />} />
          <Route path="/ip-details" element={<IPDetails />} />
          <Route path="/wage-register" element={<SalaryAll company={true} />} />
          <Route path="/payments" element={<PaymentAll company={true} />} />
          <Route path="/attendance" element={<AttendanceAll company={true} />} />
          <Route 
            path="/company-wage-slips" 
            element={<CompanyWageSlips />} 
          />

        </Routes>
      </main>
    </div>
  );
};

export default Employee;
