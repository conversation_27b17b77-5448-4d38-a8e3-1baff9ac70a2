import React, { useState, useEffect, useCallback, useRef } from "react";
import salariesApi from "../../apis/salaries";
import PageLoader from "../../components/PageLoader";
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import {  getCurrentYearMonth } from "../../utils/datetime";
import DateFilter from "../Payment/All/DateFilter";

import { useParams } from "react-router-dom";
const monthMap = {
  January: "01",
  February: "02",
  March: "03",
  April: "04",
  May: "05",
  June: "06",
  July: "07",
  August: "08",
  September: "09",
  October: "10",
  November: "11",
  December: "12",
};

const PayrollManagement = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [period, setPeriod] = useState(getCurrentYearMonth());
  const [selectedMonth, setSelectedMonth] = useState();
  const [selectedYear, setSelectedYear] = useState();
  const [salaryData, setSalaryData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalGrossAmount, setTotalGrossAmount] = useState(true);
  const companyData = JSON.parse(localStorage.getItem("selected-company"));
  const [selectedItem, setSelectedItem] = useState(null);
  const { companyId } = useParams();

  const handleOpenModal = (item) => {
    setSelectedItem(item);
    setisIpEditModalOpen(true);
  };


  const fetchSalaryData = async () => {
    setIsLoading(true);
    try {
      if(!companyId) return

      const { data } = await salariesApi.getSalaryData({ period,companyId });
      setSalaryData(data.salaries || []);
    } catch (error) {
      if (error.response?.status === 404) {
        setSalaryData([]);
      }
      console.error("Error fetching salary data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const totalGrossAmount = salaryData.reduce(
      (total, item) => total + (item.net_amount || 0),
      0
    );
    setTotalGrossAmount(totalGrossAmount);
  }, [salaryData]);

  useEffect(() => {
    const year = period.toString().slice(0, 4);
    const month = period.toString().slice(4, 6);
    fetchSalaryData();
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const monthName = months[parseInt(month, 10) - 1];
    setSelectedMonth(monthName);
    setSelectedYear(year);
  }, [period]);





  const exportSalaryTransfer = {
    excel: (salaryData, companyData, selectedMonth, totalGrossAmount) => {
      const sheetData = salaryData.map((item, i) => {
        if (item.employee?.bank_account_number === "0") return null;
        const sbiParts = [
          item?.employee?.cmp_code,
          companyData.name,
          companyData.bank_account_number,
          item.employee?.ifsc?.startsWith("SBIN") ? "DCR" : "NEFT",
        ];
    
        return {
          "S. No": i + 1,
          "Employee ID": item.employee?.employee_id || " ",
          "Client Code": item.employee?.client_id || " ",
          "SBI Description": sbiParts[0] || "",
          " ": sbiParts[1] || "",
          "  ": sbiParts[2] || "",
          "": sbiParts[3] || "",
          Salary: item.net_amount?.toLocaleString(),
          Name: item.employee?.name || `Staff ${i + 1}`,
          IFSC: item.employee?.ifsc || "A3CD0000000",
          "Account No": item.employee?.bank_account_number || "**************",
        };
      }).filter(item => item !== null);
    
      sheetData.push({
        "S. No": "Total",
        "SBI Description": "",
        " ": "",
        "  ": "",
        "": "",
        Salary: totalGrossAmount?.toLocaleString(),
        Name: "",
        IFSC: "",
        "Account No": "",
      });
  
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(sheetData);
      XLSX.utils.book_append_sheet(wb, ws, `Salary Transfer - ${selectedMonth}`);
      XLSX.writeFile(wb, `Salary_Transfer_${selectedMonth}.xlsx`);
    },
  
    pdf: (salaryData, companyData, selectedMonth, selectedYear, totalGrossAmount) => {
      const doc = new jsPDF('l', 'mm', 'a4');
      
      doc.setFontSize(16);
      doc.text(companyData.name, doc.internal.pageSize.width / 2, 15, { align: 'center' });
      doc.setFontSize(12);
      doc.text(`SALARY TRANSFER DETAILS OF WAGE MONTH ${selectedMonth} - ${selectedYear}`, 
        doc.internal.pageSize.width / 2, 25, { align: 'center' });
  
      const tableData = salaryData.map((item, i) => {
        if (item.employee?.bank_account_number === "0") return null;
        const sbiParts = [
          item?.employee?.cmp_code,
          companyData.name,
          companyData.bank_account_number,
          item.employee?.ifsc?.startsWith("SBIN") ? "DCR" : "NEFT",
        ];
  
        return [
          i + 1,
          item.employee?.employee_id || " ",
          item.employee?.client_id || " ",
          sbiParts.join(" - "),
          item.net_amount?.toLocaleString(),
          item.employee?.name || `Staff ${i + 1}`,
          item.employee?.ifsc || "A3CD0000000",
          item.employee?.bank_account_number || "**************",
        ];
      }).filter(item => item !== null);
  
      tableData.push([
        "Total",
        "",
        totalGrossAmount?.toLocaleString(),
        "",
        "",
        "",
      ]);
  
      doc.autoTable({
        startY: 35,
        head: [['S. No','Employee ID','Client Code', 'SBI Description', 'Salary', 'Name', 'IFSC', 'Account No']],
        body: tableData,
        theme: 'grid',
        styles: { fontSize: 8, cellPadding: 2 },
        headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' },
      });
  
      doc.save(`Salary_Transfer_${selectedMonth}.pdf`);
    }
  };
  
  // Export functions for Contribution Details
  const exportContribution = {
    excel: (salaryData, companyData, selectedMonth, selectedYear) => {
      const sheetData = salaryData.map((item, i) => ({
        "S.no": i + 1,
        "IP Name": item?.employee?.name,
        "Contribution": item?.employee?.contribution
      }));
  
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(sheetData);
      XLSX.utils.book_append_sheet(wb, ws, `Contribution - ${selectedMonth}`);
      XLSX.writeFile(wb, `Contribution_Details_${selectedMonth}.xlsx`);
    },
  
    pdf: (salaryData, companyData, selectedMonth, selectedYear) => {
      const doc = new jsPDF('l', 'mm', 'a4');
      
      doc.setFontSize(16);
      doc.text(companyData.name, doc.internal.pageSize.width / 2, 15, { align: 'center' });
      doc.setFontSize(12);
      doc.text(`Contribution Detail of MONTH ${selectedMonth?.toUpperCase()} - ${selectedYear}`, 
        doc.internal.pageSize.width / 2, 25, { align: 'center' });
      const tableData = salaryData.map((item, i) => [
        i + 1,
        item?.employee?.name,
        item?.employee?.contribution
      ]);
  
      doc.autoTable({
        startY: 35,
        head: [['S.no', 'IP Name', 'Contribution']],
        body: tableData,
        theme: 'grid',
        styles: { fontSize: 8, cellPadding: 2 },
        headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' },
      });
  
      doc.save(`Contribution_Details_${selectedMonth}.pdf`);
    }
  };



 

  
  

  const Tables = () => {
    switch (activeTab) {
      case 0:
        return (
          <div className="w-full p-6 text-gray-900">
            <div className="text-center mb-6 p-4 rounded">
              <h2 className="text-2xl font-bold mb-3">{companyData.name}</h2>
              <p className="text-base mb-4">
                SALARY TRANSFER DETAILS OF WAGE MONTH {selectedMonth} - {selectedYear}
              </p>
              <button
                className="px-4 py-2 text-black bg-white border border-black rounded text-sm font-medium"
                onClick={() => exportSalaryTransfer.excel(salaryData, companyData, selectedMonth, totalGrossAmount)}
              >
                Export to Excel
              </button>
              <button
                className="px-4 py-2 text-black bg-white border border-black rounded text-sm font-medium"
                onClick={() => exportSalaryTransfer.pdf(salaryData, companyData, selectedMonth, selectedYear, totalGrossAmount)}
              >
                Export to PDF
              </button>
            </div>
            <div className="overflow-x-auto rounded-lg p-4">
              <table className="w-full border-collapse bg-white">
                <thead>
                  <tr>
                    <th className="border-2 border-gray-100 bg-gray-100 text-black p-3 text-base font-semibold">
                      S. No
                    </th>
                    <th className="border-2 border-gray-100 bg-gray-100 text-black p-3 text-base font-semibold">
                      Employee ID
                     </th>
                     <th className="border-2 border-gray-100 bg-gray-100 text-black p-3 text-base font-semibold">
                     Client Code
                     </th>
                    <th className="border-2 border-gray-100 bg-gray-100 text-black p-3 text-base font-semibold">
                      SBI Description
                    </th>
                    <th className="border-2 border-gray-100 bg-gray-100 text-black p-3 text-base font-semibold">
                      Salary
                    </th>
                    <th className="border-2 border-gray-100 bg-gray-100 text-black p-3 text-base font-semibold">
                      Name
                    </th>
                    <th className="border-2 border-gray-100 bg-gray-100 text-black p-3 text-base font-semibold">
                      IFSC
                    </th>
                    <th className="border-2 border-gray-100 bg-gray-100 text-black p-3 text-base font-semibold">
                      Account No
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {salaryData.map((item, i) => {
                    console.log(item);
                    if (item.employee?.bank_account_number === "0") return null;
                    const sbiParts = [
                      item?.employee?.cmp_code,
                      companyData.name,
                      companyData.bank_account_number,
                      item.employee?.ifsc?.startsWith("SBIN") ? "DCR" : "NEFT",
                    ];
  
                    return (
                      <tr key={i} className="bg-white hover:bg-gray-50">
                        <td className="border-2 border-gray-100 p-3 text-sm">
                          {i + 1}
                        </td>
                        <td className="border-2 border-gray-100 p-3 text-sm font-mono">
                          {item.employee?.employee_id || "N/A"}
                        </td>
                        <td className="border-2 border-gray-100 p-3 text-sm font-mono">
                          {item.employee?.client?.client_code || "N/A"}
                        </td>

                        <td className="border-2 border-gray-100 p-3 text-sm">
                          {sbiParts.join(" - ")}
                        </td>
                        <td className="border-2 border-gray-100 p-3 text-sm text-right font-medium">
                          {item.net_amount?.toLocaleString()}
                        </td>
                        <td className="border-2 border-gray-100 p-3 text-sm">
                          {item.employee?.name || `Staff ${i + 1}`}
                        </td>
                        <td className="border-2 border-gray-100 p-3 text-sm font-mono">
                          {item.employee?.ifsc || "A3CD0000000"}
                        </td>
                        <td className="border-2 border-gray-100 p-3 text-sm font-mono">
                          {item.employee?.bank_account_number || "**************"}
                        </td>
                      </tr>
                    );
                  })}
                  <tr>
                    <td
                      colSpan={2}
                      className="border-2 border-gray-100 p-3 text-right font-bold text-base"
                    >
                      Total
                    </td>
                    <td className="border-2 border-gray-100 p-3 text-right font-bold text-base">
                      {totalGrossAmount?.toLocaleString()}
                    </td>
                    <td colSpan={3} className="border-2 border-gray-100 p-3"></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        );
  
   
  
    
      case 3:
      
          return (
            <div className="w-full p-6 text-gray-900">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold mb-3">{companyData.name}</h2>
                <p className="text-base">
                  Contribution Detail of MONTH {selectedMonth?.toUpperCase()} - {selectedYear}
                </p>
                <div className="space-x-2">
              <button
                className="px-4 py-2 text-black bg-white border border-black rounded text-sm font-medium"
                onClick={() => exportContribution.excel(salaryData, companyData, selectedMonth, selectedYear)}
              >
                Export to Excel
              </button>
              <button
                className="px-4 py-2 text-black bg-white border border-black rounded text-sm font-medium"
                onClick={() => exportContribution.pdf(salaryData, companyData, selectedMonth, selectedYear)}
              >
                Export to PDF
              </button>
            </div>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border p-3 text-left text-base font-semibold">S.no</th>
                      <th className="border p-3 text-left text-base font-semibold">IP Name</th>

                      <th className="border p-3 text-left text-base font-semibold">Contribution</th>
                    </tr>
                  </thead>
                  <tbody>
                    {salaryData.map((item, index) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="border p-3 text-sm">{index + 1}</td>
                        
                        <td className="border p-3 text-sm">{item?.employee?.name}</td>
                        <td className="border p-3 text-sm">{item?.employee?.contribution}</td>
                       
                       
                      </tr>
                    ))}
                  </tbody>
                </table>
    
              
              </div>
            </div>
          );
    
  
      default:
        return null;
    }
  };
  
  if (isLoading) {
    return <PageLoader />;
  }
  
  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-green-500 text-black py-8 px-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold mb-3 text-black">
              Payroll Management
            </h1>
            <p className="text-lg text-black">
              Current Period: {selectedMonth}
            </p>
          </div>
          <DateFilter period={period} setPeriod={setPeriod} />
        </div>
      </div>
  
      {/* Main Content */}
      <div className="p-6">
        <div className="rounded-lg overflow-hidden shadow-sm">
          <div className="flex space-x-6 p-4 border-b bg-white">
            <button
              onClick={() => setActiveTab(0)}
              className={`px-6 py-2.5 rounded transition-colors duration-200 ease-in-out text-base font-medium ${
                activeTab === 0
                  ? "bg-green-500 text-white"
                  : "bg-white text-gray-800 hover:bg-green-50"
              }`}
            >
              Salary Transfer
            </button>
           
            <button
              onClick={() => setActiveTab(3)}
              className={`px-6 py-2.5 rounded transition-colors duration-200 ease-in-out text-base font-medium ${
                activeTab === 3
                  ? "bg-green-500 text-white"
                  : "bg-white text-gray-800 hover:bg-green-50"
              }`}
            >
              Contribution Details
            </button>
          </div>
          <div className="bg-white">
            <Tables />
          </div>
        </div>
      </div>
    </div>
  )
};

export default PayrollManagement;
