/* eslint-disable react/prop-types */

const Table = ({ salaries, onSelect }) => {
  console.log(salaries)
  return (
    <table className="bg-white w-full rounded overflow-hidden">
      <thead className="border-b bg-gray-100 text-left text-sm leading-tight">
        <tr>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            Sl No.
          </th>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            Name of the workmen
          </th>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            Sl No. in the register of work men
          </th>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            Designation / Nature of work done
          </th>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            No. of days worked
          </th>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            Unit of work done
          </th>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            Daily rate of wages / piece rate
          </th>
          <th className="border px-2.5 py-1 font-medium" colSpan="5">
            Amount of wages earned
          </th>
          <th className="border px-2.5 py-1 font-medium" colSpan="3"></th>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            Signature / Thumb-impression of workman
          </th>
          <th className="border px-2.5 py-1 font-medium" rowSpan="2">
            Initial of contractor or his respresentative
          </th>
        </tr>
        <tr>
          <th className="border px-2.5 py-1 font-medium">Basic wages</th>
          <th className="border px-2.5 py-1 font-medium">Bonus</th>
          <th className="border px-2.5 py-1 font-medium">
            Allowance
          </th>
          {/* <th className="border px-2.5 py-1 font-medium">
            Dearness Allowances
          </th> */}
          <th className="border px-2.5 py-1 font-medium">Overtime</th>
          <th className="border px-2.5 py-1 font-medium">
            Other cash payments
          </th>
          <th className="border px-2.5 py-1 font-medium">Total</th>
          <th className="border px-2.5 py-1 font-medium" colSpan="2">
            Deductions, if any
          </th>
          <th className="border px-2.5 py-1 font-medium">Net amount paid</th>
        </tr>
      </thead>

      <tbody className="divide-y">
        {salaries?.map((item) => (
          <tr
            key={item.idx}
            className="hover:bg-gray-100 cursor-pointer"
            onClick={() => onSelect(item.salary.id, item.employee.client?.id)}
          >
            <td className="px-2.5 py-1.5 border">{item.idx}</td>
            <td className="px-2.5 py-1.5 border">{item.employee?.name}</td>
            <td className="px-2.5 py-1.5 border">
              {item.employee?.employee_id}
            </td>
            <td className="px-2.5 py-1.5 border">
              {/* TODO: Designation / Nature of work done */}
            </td>
            <td className="px-2.5 py-1.5 border">{item.salary?.days_worked}</td>
            <td className="px-2.5 py-1.5 border">
              {/* TODO: Unit of work done */}
            </td>
            <td className="px-2.5 py-1.5 border">{item.salary.daily_rate}</td>
            <td className="px-2.5 py-1.5 border">
              {/* TODO: Split if basic wages and gross amount are different */}
              {item.salary.gross_amount}
            </td>
            <td className="px-2.5 py-1.5 border">
              {item.employee?.bonus && item.salary?.days_worked
                ? (item.employee.bonus * item.salary.days_worked).toFixed(2)
                : ""}
            </td>
            <td className="px-2.5 py-1.5 border">
              {item.employee?.allowance && item.salary?.days_worked
                ? (item.employee.allowance * item.salary.days_worked).toFixed(2)
                : ""}
            </td>
            {/* <td className="px-2.5 py-1.5 border">
              
            </td> */}

            <td className="px-2.5 py-1.5 border">{/* TODO: Overtime */}</td>
            <td className="px-2.5 py-1.5 border">
              {/* TODO: Other cash payments */}
            </td>
            <td className="px-2.5 py-1.5 border">{item.salary.gross_amount?.toFixed(2)}</td>
            <td className="px-2.5 py-1.5 border">{item.salary.epf?.toFixed(2)}</td>
            <td className="px-2.5 py-1.5 border">{item.salary.esi?.toFixed(2)}</td>
            <td className="px-2.5 py-1.5 border"></td>
            <td className="px-2.5 py-1.5 border">
              {/* TODO: Signature / Thumb-impression of workman */}
            </td>
            <td className="px-2.5 py-1.5 border">
              {(item.salary?.net_amount || 0).toFixed(2)}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default Table;
