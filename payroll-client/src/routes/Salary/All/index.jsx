import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import salariesApi from "../../../apis/salaries";
import PageLoader from "../../../components/PageLoader";
import Error from "../../../components/Error";
import { getCurrentYearMonth } from "../../../utils/datetime";
import { getWagesRegisterData } from "../../../utils/salary";
import DateFilter from "./DateFilter";
import Table from "./Table";
import jsPDF from "jspdf";
import * as XLSX from "xlsx";
import "jspdf-autotable";
import { formatPeriod } from "../../../utils/utils";

const All = ({ company }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [period, setPeriod] = useState(getCurrentYearMonth());
  const [salaries, setSalaries] = useState();
  const companydata = JSON.parse(localStorage.getItem("selected-company"));
  const clientData = JSON.parse(localStorage.getItem("selected-client"));

  const { clientId, companyId } = useParams();
  const navigate = useNavigate();

  const fetchData = async () => {
    setIsLoading(true);
    try {
      let data;
      if (!company) {
        const res = await salariesApi.fetchAll({
          clientId,
          period,
        });
        data = res.data;
        setSalaries(getWagesRegisterData(data.salaries));
      } else {
        const res = await salariesApi.companyFetchAll({
          companyId,
          period,
        });

        
        const transformedData = res.data.salaries.flatMap(clientGroup => {
          return clientGroup.employees.map(employee => {
            const salary = employee.salaries?.[0] || {
              id: employee.id,
              days_worked: 0,
              daily_rate: 0,
              gross_amount: 0,
              net_amount: 0,
              advance: 0,
              epf: 0,
              esi: 0,
              epf_employer: 0,
              esi_employer: 0,
              actual_gross_amount: 0,
              service_charge: 0,
              billable_amount: 0
            };

           

            return {
              idx: employee.employee_id,
              id: salary.id ,
              employee: {
                id: employee.id,
                name: employee.name,
                employee_id: employee.employee_id,
                status: employee.status,
                epf_uan: employee.epf_uan,
                esi_number: employee.esi_number,
                bank_account_number: employee.bank_account_number,
                ifsc: employee.ifsc,
                bank_name: employee.bank_name,
                client: clientGroup.client,
                allowance: Number(employee.allowance),
                bonus: Number(employee.bonus),

              },
              salary: {
                ...salary,
                id: salary.id ,
              }
            };
          });
        });

        setSalaries(transformedData);
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const onSelect = async (salaryId, clientId) => {
    
    if (company) {
      navigate(`/${companyId}/clients/${clientId}/salaries/${salaryId}`);
    } else {
      navigate(salaryId);
    }
  };

  useEffect(() => {
    fetchData();
  }, [clientId, period, companyId]);

  if (isLoading) {
    return <PageLoader />;
  }
  if (!salaries) {
    return <Error title="Could not load salaries" />;
  }
  const downloadPDF = (clientSalaries, clientInfo) => {
    const doc = new jsPDF({
      orientation: "landscape",
      unit: "mm",
      format: "a4",
    });

    const pageWidth = doc.internal.pageSize.width;
    const leftMargin = 10;
    let yPos = 15;

    // Header section
    doc.setFont("helvetica", "bold");
    doc.setFontSize(16);
    doc.text("FORM XVI", pageWidth / 2, yPos, { align: "center" });
    yPos += 7;

    doc.setFontSize(11);
    doc.text("See Rule 78(a)(i)", pageWidth / 2, yPos, { align: "center" });
    yPos += 7;

    doc.text("Register of Wages", pageWidth / 2, yPos, { align: "center" });
    yPos += 10;

    // Improved header details handling
    doc.setFontSize(10);
    const labelWidth = 85;
    const valueWidth = pageWidth - leftMargin - labelWidth - 25;

    const headerDetails = [
      {
        label: "Name and Address of Contractor:",
        value: `${companydata?.name}, ${companydata?.address_1}`
      },
      {
        label: "Nature and location of work:",
        value: `${clientInfo.contract_category || ''}, ${clientInfo.address || ''}`
      },
      {
        label: "Name and address of establishment in/under which contract is carried on:",
        value: "ASSISTANT COMMISSIONER OF INCOME TAX, SHASTHRI ROAD, PUBLIC LIBRARY BUILDING, KOTTAYAM, KERALA-686001"
      },
      {
        label: "Name and address of Principal Employer:",
        value: "INCOME TAX OFFICE, KOTTAYAM"
      },
      {
        label: "Period:",
        value: formatPeriod(period)
      }
    ];

    headerDetails.forEach(({ label, value }) => {
      doc.setFont("helvetica", "bold");
      const splitLabel = doc.splitTextToSize(label, labelWidth);
      doc.text(splitLabel, leftMargin, yPos);

      doc.setFont("helvetica", "normal");
      const splitValue = doc.splitTextToSize(value || '', valueWidth);
      doc.text(splitValue, leftMargin + labelWidth, yPos);

      const labelLines = splitLabel.length;
      const valueLines = splitValue.length;
      const lineHeight = 5;
      const maxLines = Math.max(labelLines, valueLines);
      yPos += maxLines * lineHeight + 2;
    });

    const headers = [
      [
        "Sl No.",
        "Name of the workmen",
        "Sl No. in register",
        "Designation / Nature of work done",
        "No. of days worked",
        "Unit of work done",
        "Daily rate of wages",
        "Basic wages",
        "Bonus",
        "Allowances",
        "Dearness Allowances",
        "Overtime",
        "Other cash payments",
        "Total",
        "EPF",
        "ESI",
        "Net amount paid",
      ]
    ];

    const data = clientSalaries.map((item) => [
      item.idx,
      item.employee.name,
      "",
      "",
      item.salary.days_worked,
      "",
      item.salary.daily_rate,
      item.salary.gross_amount,
      (item.employee?.bonus && item.salary?.days_worked 
        ? item.employee.bonus * item.salary.days_worked 
        : 0),
      (item.employee?.allowance && item.salary?.days_worked 
        ? item.employee.allowance * item.salary.days_worked 
        : 0),

      "",
      "",
      "",

      item.salary.gross_amount,
      item.salary.epf,
      item.salary.esi,
      
          (item.salary?.net_amount || 0).toFixed(2)
      
      
      
      
    ]);

    doc.autoTable({
      head: headers,
      body: data,
      startY: yPos + 5,
      styles: {
        fontSize: 8,
        cellPadding: 2,
      },
      headStyles: {
        fillColor: [240, 240, 240],
        textColor: [0, 0, 0],
        fontStyle: 'bold',
        halign: 'center',
        valign: 'middle'
      },
      columnStyles: {
        0: { cellWidth: 12 },
        1: { cellWidth: 25 },
        2: { cellWidth: 15 },
      },
      theme: 'grid',
      tableWidth: 'auto',
      margin: { left: leftMargin },
    });

    doc.save(`salary_report_${clientInfo.name}.pdf`);
  };

  const handleExportExcel = (clientSalaries) => {
    const sheetData = clientSalaries.map((item, i) => ({
      "Sl No.": i + 1,
      "Name of the workmen": item.employee.name,
      "Sl No. in register": "",
      "Designation / Nature of work done": "",
      "No. of days worked": item.salary.days_worked,
      "Unit of work done": "",
      "Daily rate of wages": item.salary.daily_rate,
      "Basic wages": item.salary.gross_amount,
      "Bonus":(item.employee?.bonus && item.salary?.days_worked 
        ? item.employee.bonus * item.salary.days_worked 
        : 0),
      "Allowance":(item.employee?.allowance && item.salary?.days_worked 
        ? item.employee.allowance * item.salary.days_worked 
        : 0),
      "Dearness Allowances": "",
      Overtime: "",
      "Other cash payments": "",
      Total: item.salary.gross_amount,
      EPF: item.salary.epf,
      ESI: item.salary.esi,
      "Net amount paid":(item.salary?.net_amount || 0).toFixed(2)
      
    }));

    const ws = XLSX.utils.json_to_sheet(sheetData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `Salary_Report`);

    XLSX.writeFile(wb, `Salary_Report_${clientSalaries[0]?.employee?.client?.name || 'Report'}.xlsx`);
  };

  const downloadAllPDF = () => {
    const doc = new jsPDF({
      orientation: "landscape",
      unit: "mm",
      format: "a4",
    });

    const renderHeaderSection = (clientInfo, yPosition) => {
      const pageWidth = doc.internal.pageSize.width;
      const leftMargin = 10;
      let yPos = yPosition;

      // Header section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(16);
      doc.text("FORM XVI", pageWidth / 2, yPos, { align: "center" });
      yPos += 7;

      doc.setFontSize(11);
      doc.text("See Rule 78(a)(i)", pageWidth / 2, yPos, { align: "center" });
      yPos += 7;

      doc.text("Register of Wages", pageWidth / 2, yPos, { align: "center" });
      yPos += 10;

      // Improved header details handling
      doc.setFontSize(10);
      const labelWidth = 85;
      const valueWidth = pageWidth - leftMargin - labelWidth - 25;

      const headerDetails = [
        {
          label: "Name and Address of Contractor:",
          value: `${companydata?.name}, ${companydata?.address_1}`
        },
        {
          label: "Nature and location of work:",
          value: `${clientInfo.contract_category || ''}, ${clientInfo.address || ''}`
        },
        {
          label: "Name and address of establishment in/under which contract is carried on:",
          value: "ASSISTANT COMMISSIONER OF INCOME TAX, SHASTHRI ROAD, PUBLIC LIBRARY BUILDING, KOTTAYAM, KERALA-686001"
        },
        {
          label: "Name and address of Principal Employer:",
          value: "INCOME TAX OFFICE, KOTTAYAM"
        },
        {
          label: "Period:",
          value: formatPeriod(period)
        }
      ];

      headerDetails.forEach(({ label, value }) => {
        doc.setFont("helvetica", "bold");
        const splitLabel = doc.splitTextToSize(label, labelWidth);
        doc.text(splitLabel, leftMargin, yPos);

        doc.setFont("helvetica", "normal");
        const splitValue = doc.splitTextToSize(value || '', valueWidth);
        doc.text(splitValue, leftMargin + labelWidth, yPos);

        const labelLines = splitLabel.length;
        const valueLines = splitValue.length;
        const lineHeight = 5;
        const maxLines = Math.max(labelLines, valueLines);
        yPos += maxLines * lineHeight + 2;
      });

      return yPos;
    };

    const renderTable = (data, startY) => {
      const headers = [
        [
          "Sl No.",
          "Name of the workmen",
          "Sl No. in register",
          "Designation / Nature of work done",
          "No. of days worked",
          "Unit of work done",
          "Daily rate of wages",
          "Basic wages",
          "Dearness Allowances",
          "Overtime",
          "Other cash payments",
          "Total",
          "EPF",
          "ESI",
          "Net amount paid",
        ]
      ];

      doc.autoTable({
        head: headers,
        body: data,
        startY: startY + 5,
        styles: {
          fontSize: 8,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [240, 240, 240],
          textColor: [0, 0, 0],
          fontStyle: 'bold',
          halign: 'center',
          valign: 'middle'
        },
        columnStyles: {
          0: { cellWidth: 12 },
          1: { cellWidth: 25 },
          2: { cellWidth: 15 },
        },
        theme: 'grid',
        tableWidth: 'auto',
        margin: { left: 10, right: 10 },
      });

      return doc.lastAutoTable.finalY;
    };

    const clientGroups = Object.values(salaries.reduce((acc, salary) => {
      const clientId = salary.employee.client.id;
      if (!acc[clientId]) {
        acc[clientId] = {
          client: salary.employee.client,
          salaries: []
        };
      }
      acc[clientId].salaries.push(salary);
      return acc;
    }, {}));

    clientGroups.forEach((clientGroup, index) => {
      if (index > 0) {
        doc.addPage();
      }

      let yPos = 15;
      yPos = renderHeaderSection(clientGroup.client, yPos);

      const tableData = clientGroup.salaries.map((item, idx) => [
        item.idx,
        item.employee.name,
        "",
        "",
        item.salary.days_worked,
        "",
        item.salary.daily_rate,
        item.salary.gross_amount,
        "",
        "",
        "",
        item.salary.gross_amount,
        item.salary.epf,
        item.salary.esi,
        item.salary.net_amount,
      ]);

      renderTable(tableData, yPos);
    });

    doc.save(`All_Salary_Reports_${period}.pdf`);
  };

  const handleExportAllExcel = () => {
    const clientGroups = Object.values(salaries.reduce((acc, salary) => {
      const clientId = salary.employee.client.id;
      if (!acc[clientId]) {
        acc[clientId] = {
          client: salary.employee.client,
          salaries: []
        };
      }
      acc[clientId].salaries.push(salary);
      return acc;
    }, {}));

    const wb = XLSX.utils.book_new();
    
    clientGroups.forEach((clientGroup) => {
      const sheetData = clientGroup.salaries.map((item, i) => ({
        "Sl No.": i + 1,
        "Name of the workmen": item.employee.name,
        "Sl No. in register": "",
        "Designation / Nature of work done": "",
        "No. of days worked": item.salary.days_worked,
        "Unit of work done": "",
        "Daily rate of wages": item.salary.daily_rate,
        "Basic wages": item.salary.gross_amount,
        "Dearness Allowances": "",
        Overtime: "",
        "Other cash payments": "",
        Total: item.salary.gross_amount,
        EPF: item.salary.epf,
        ESI: item.salary.esi,
        "Net amount paid": item.salary.net_amount,
      }));

      const ws = XLSX.utils.json_to_sheet(sheetData);
      
      let sheetName = clientGroup.client.name
        .replace(/[\\/?*[\]]/g, '') 
        .trim()
        .substring(0, 31); 
      
      let counter = 1;
      let originalSheetName = sheetName;
      while (wb.SheetNames.includes(sheetName)) {
        const suffix = ` (${counter})`;
        sheetName = originalSheetName.substring(0, 31 - suffix.length) + suffix;
        counter++;
      }

      XLSX.utils.book_append_sheet(wb, ws, sheetName);
    });

    XLSX.writeFile(wb, `All_Salary_Reports_${period}.xlsx`);
  };

  return (
    <>
      <header className="w-full flex justify-between items-center gap-5 mb-5">
        <h2 className="text-2xl leading-none font-semibold">
          Register of Wages
        </h2>
        <div className=" items-center gap-2">
         
          <div>
          <DateFilter period={period} setPeriod={setPeriod} />
          </div>
         <div className="mt-4">
         {company && salaries?.length > 0 && (
            <>
              <button
                onClick={downloadAllPDF}
                className="px-4 py-2 text-black bg-white border border-black rounded text-sm font-medium"
              >
                Download All PDF
              </button>
              <button
                onClick={handleExportAllExcel}
                className="px-4 py-2 text-black bg-white border border-black rounded text-sm font-medium"
              >
                Export All
              </button>
            </>
          )}
         </div>
        </div>
        
      </header>
      {salaries?.length > 0 ? (
        <div className="space-y-8">
          {company ? (
            Object.values(salaries.reduce((acc, salary) => {
              const clientId = salary.employee.client.id;
              if (!acc[clientId]) {
                acc[clientId] = {
                  client: salary.employee.client,
                  salaries: []
                };
              }
              acc[clientId].salaries.push(salary);
              return acc;
            }, {})).map((clientGroup, index) => (
              <section key={clientGroup.client.id} className="max-w-full overflow-x-auto pb-1 rounded scrollbar">
                <div className="p-4 rounded-md shadow-md text-black font-bold">
                  <h3 className="text-lg font-bold text-center">FORM XVI</h3>
                  <p className="text-sm font-bold text-center">See Rule 78(a)(i)</p>
                  <p className="text-sm font-bold text-center">Register of Wages</p>
                  <p className="text-sm">
                    Name and Address of Contractor :
                    <span className="font-thin text-sm">
                      {`${companydata?.name}, ${companydata?.address_1}`}
                    </span>
                  </p>
                  <p className="text-sm">
                    Nature and location of work :
                    <span className="font-thin text-sm">
                      {`${clientGroup.client.contract_category || ''}`}
                    </span>
                  </p>
                  <p className="text-sm">
                    Name and address of establishment in/under which contract is carried on :
                    <span className="font-thin text-sm">
                      {clientGroup.client.address || ''}
                    </span>
                  </p>
                  <p className="text-sm">
                    Name and address of Principal Employer :
                    <span className="font-thin text-sm">
                      {`${clientGroup.client.name || ''}, ${clientGroup.client.address || ''}`}
                    </span>
                  </p>
                  {period && (
                    <p className="text-sm">
                      Period:{" "}
                      {`${period.toString().slice(0, 4)} ${period.toString().slice(4)}`}
                    </p>
                  )}

                 
                </div>
            
                <Table 
                  salaries={clientGroup.salaries} 
                  onSelect={(salaryId, clientId) => onSelect(salaryId, clientId)}
                  showClientInfo={false}
                />
              </section>
            ))
          ) : (
            <section className="max-w-full overflow-x-auto pb-1 rounded scrollbar">
              <div className=" p-4 rounded-md shadow-md text-black font-bold">
                <h3 className="text-lg font-bold text-center">FORM XVI</h3>
                <p className="text-sm font-bold text-center">See Rule 78(a)(i)</p>
                <p className="text-sm font-bold text-center">Register of Wages</p>
                <p className="text-sm">
                  Name and Address of Contractor :
                  <span className="font-thin text-sm">
                    {company 
                      ? `${companydata?.name}, ${companydata?.address_1}`
                      : `${companydata?.name}, ${companydata?.address_1}`
                    }
                  </span>
                </p>
                <p className="text-sm">
                  Nature and location of work :
                  <span className="font-thin text-sm">
                    {company 
                      ? `${salaries[0]?.employee?.client?.contract_category || ''}}`
                      : `${clientData?.contract_category}`
                    }
                  </span>
                </p>
                <p className="text-sm">
                  Name and address of establishment in/under which contract is carried on :
                  <span className="font-thin text-sm">
                    {company 
                      ? salaries[0]?.employee?.client?.address || ''
                      : clientData?.address
                    }
                  </span>
                </p>
                <p className="text-sm">
                  Name and address of Principal Employer :
                  <span className="font-thin text-sm">
                    {company 
                      ? `${salaries[0]?.employee?.client?.name || ''}, ${salaries[0]?.employee?.client?.address || ''}`
                      : `${clientData?.name}, ${clientData?.address}`
                    }
                  </span>
                </p>
                {period && (
                  <p className="text-sm">
                    Period:{" "}
                    {`${period.toString().slice(0, 4)} ${period
                      .toString()
                      .slice(4)}`}
                  </p>
                )}

                <div className="flex justify-end">
                  <button
                    onClick={() => {
                      downloadPDF(salaries, company ? companydata : clientData);
                    }}
                  
                    
                    className="px-4 py-2 text-black bg-white border border-black rounded ml-1  text-sm font-medium"
                  >
                    Download PDF
                  </button>
                  <button
                    onClick={() => handleExportExcel(salaries)}
                    className="px-4 py-2 text-black bg-white border border-black rounded ml-1  text-sm font-medium"
                  >
                    Export to Excel
                  </button>
                </div>
              </div>
              <Table 
                salaries={salaries} 
                onSelect={(salaryId) => onSelect(salaryId)}
                showClientInfo={false}
              />
            </section>
          )}
        </div>
      ) : (
        <div className="bg-gray-100 p-5 text-gray-600 text-center rounded">
          No data for the selected period
        </div>
      )}
    </>
  );
};

export default All;
