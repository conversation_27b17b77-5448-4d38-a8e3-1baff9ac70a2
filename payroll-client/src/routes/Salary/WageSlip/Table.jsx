/* eslint-disable react/prop-types */

const Table = ({ data, centering = false }) => {
  return (
    <table className="bg-white rounded overflow-hidden">
      <tbody>
        {data?.map(({ key, value }) => (
          <tr key={key}>
            <td width="430px" className="border px-2.5 py-1.5">
              {key}
            </td>
            <td
              className={`border px-2.5 py-1.5 min-w-36${
                centering ? " text-center" : ""
              }`}
            >
              {value}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default Table;
