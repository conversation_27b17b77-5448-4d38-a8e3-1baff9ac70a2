import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import salariesApi from "../../../apis/salaries";
import { getWageSlipData } from "../../../utils/salary";
import { generatePDF, generateExcel } from "../../../utils/export";
import PageLoader from "../../../components/PageLoader";
import Error from "../../../components/Error";
import Table from "./Table";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import "jspdf-autotable";
import DateFilter from "../All/DateFilter";
import { getCurrentYearMonth } from "../../../utils/datetime";
import * as XLSX from "xlsx";
import { formatPeriod } from "../../../utils/utils";

const CompanyWageSlips = () => {
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const [employees, setEmployees] = useState([]);
  const [period, setPeriod] = useState(getCurrentYearMonth());
  const { companyId } = useParams();
  const containerRef = useRef(null);


  

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const { data } = await salariesApi.fetchCompanyDataByPeriod(companyId, period);
      const companydata = JSON.parse(localStorage.getItem("selected-company"));
      const formattedEmployees = data.employees.map(emp => {
        const salary = emp.salaries[0] || {};

        return {
          basic: [
            {
              key: "NAME AND ADDRESS OF CONTRACTOR",
              value:
               `${companydata?.name}, ${companydata?.address_1}`,
            },
            {
              key: "Nature and location of work",
              value: `${emp.client?.contract_category}, ${emp.client?.address}`,
            },
            {
              key: "Name of the employee",
              value: emp?.name,
            },
            {
              key: "Staff ID #",
              value: emp?.employee_id,
            },
            {
              key: "Salary Period",
              value: formatPeriod(period),
            },
            {
              key: "Name and Father's / Husband's name of the workman",
              value: "",
            },
            {
              key: "For the Week / Fortnight / Month ending",
              value: "",
            },
          ],
          pay: [
            { key: "Days Worked", value: salary.days_worked || 0 },
            { key: "Daily Rate", value: salary.daily_rate || emp.daily_wage },
            { key: "Gross Amount", value: salary.gross_amount || 0 },
            { key: "EPF Employee", value: salary.epf || 0 },
            { key: "ESI Employee", value: salary.esi || 0 },
            { key: "Advance", value: salary.advance || 0 },
            { key: "Net Amount", value: salary.net_amount?.toFixed(2) || 0 }
          ]
        };
      });
      setEmployees(formattedEmployees);
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const onGeneratePDF = async (employee) => {
    const doc = new jsPDF("p", "mm", "a4");
    const pageWidth = doc.internal.pageSize.width;
    const margin = 15;
  
    doc.setFontSize(14);
    doc.text("FORM XIX", pageWidth / 2, margin + 7, { align: "center" });
    doc.setFontSize(10);
    doc.text("See Rule 78(1)(b)", pageWidth / 2, margin + 14, { align: "center" });
    doc.setFontSize(12);
    doc.text("WAGE SLIP", pageWidth / 2, margin + 21, { align: "center" });
  
    const formatValue = (value) => {
      if (typeof value === 'number') {
        return value.toString();
      }
      return value?.toString()
    };
  
    doc.autoTable({
      startY: margin + 30,
      body: employee.basic.map(item => [
        item.key,
        formatValue(item.value)
      ]),
      theme: 'plain',
      styles: {
        fontSize: 9,
        cellPadding: 3,
        overflow: 'linebreak'
      },
      columnStyles: {
        0: { 
          cellWidth: 70,
          fontStyle: 'bold'
        },
        1: { 
          cellWidth: 110,
          halign: 'left'
        }
      },
      margin: { left: margin },
      didDrawCell: (data) => {
        doc.rect(data.cell.x, data.cell.y, data.cell.width, data.cell.height);
      }
    });
  
    doc.autoTable({
      startY: doc.lastAutoTable.finalY + 5,
      body: employee.pay.map(item => [
        item.key,
        formatValue(item.value)
      ]),
      theme: 'plain',
      styles: {
        fontSize: 9,
        cellPadding: 3,
        overflow: 'linebreak'
      },
      columnStyles: {
        0: { 
          cellWidth: 70,
          fontStyle: 'bold'
        },
        1: { 
          cellWidth: 110,
          halign: 'left'
        }
      },
      margin: { left: margin },
      didDrawCell: (data) => {
        doc.rect(data.cell.x, data.cell.y, data.cell.width, data.cell.height);
      }
    });
  
    doc.setFontSize(9);
    doc.text(
      "Initials of the Contractor or his Representative",
      pageWidth - margin - 35,
      doc.lastAutoTable.finalY + 15,
      { align: "right" }
    );
  
    doc.save(`WageSlip_${employee.basic[0].value}_${period}.pdf`);
  };
  
  const onGenerateExcel = (employee) => {
    const workbook = XLSX.utils.book_new();
    
    // Create worksheet for basic details
    const basicWS = XLSX.utils.json_to_sheet(
      employee.basic.map(item => ({ [item.key]: item.value }))
    );
    XLSX.utils.book_append_sheet(workbook, basicWS, "Employee_Details");
    
    // Create worksheet for pay details
    const payWS = XLSX.utils.json_to_sheet(
      employee.pay.map(item => ({ [item.key]: item.value }))
    );
    XLSX.utils.book_append_sheet(workbook, payWS, "Wage_Details");
    
    XLSX.writeFile(workbook, `WageSlip_${employee.basic[0].value}_${period}.xlsx`);
  };

  const onGenerateAllPDF = async () => {
  const doc = new jsPDF("p", "mm", "a4");
  const pageWidth = doc.internal.pageSize.width;
  const margin = 15;

  employees.forEach((employee, index) => {
    if (index !== 0) doc.addPage();

    doc.setFontSize(14);
    doc.text("FORM XIX", pageWidth / 2, margin + 7, { align: "center" });
    doc.setFontSize(10);
    doc.text("See Rule 78(1)(b)", pageWidth / 2, margin + 14, { align: "center" });
    doc.setFontSize(12);
    doc.text("WAGE SLIP", pageWidth / 2, margin + 21, { align: "center" });

    const formatValue = (value) => {
      if (typeof value === 'number') {
        return value.toString();
      }
      return value?.toString()
    };

    doc.autoTable({
      startY: margin + 30,
      body: employee.basic.map(item => [item.key, formatValue(item.value)]),
      theme: 'plain',
      styles: { fontSize: 9, cellPadding: 3, overflow: 'linebreak' },
      columnStyles: {
        0: { cellWidth: 70, fontStyle: 'bold' },
        1: { cellWidth: 110, halign: 'left' }
      },
      margin: { left: margin },
      didDrawCell: (data) => {
        doc.rect(data.cell.x, data.cell.y, data.cell.width, data.cell.height);
      }
    });

    doc.autoTable({
      startY: doc.lastAutoTable.finalY + 5,
      body: employee.pay.map(item => [item.key, formatValue(item.value)]),
      theme: 'plain',
      styles: { fontSize: 9, cellPadding: 3, overflow: 'linebreak' },
      columnStyles: {
        0: { cellWidth: 70, fontStyle: 'bold' },
        1: { cellWidth: 110, halign: 'left' }
      },
      margin: { left: margin },
      didDrawCell: (data) => {
        doc.rect(data.cell.x, data.cell.y, data.cell.width, data.cell.height);
      }
    });

    doc.setFontSize(9);
    doc.text(
      "Initials of the Contractor or his Representative",
      pageWidth - margin - 35,
      doc.lastAutoTable.finalY + 15,
      { align: "right" }
    );
  });

  doc.save(`Company_WageSlips_${period}.pdf`);
};

const onGenerateAllExcel = () => {
  const workbook = XLSX.utils.book_new();

  employees.forEach((employee, index) => {
    const basicSheet = XLSX.utils.json_to_sheet(
      employee.basic.map(item => ({ [item.key]: item.value }))
    );
    const paySheet = XLSX.utils.json_to_sheet(
      employee.pay.map(item => ({ [item.key]: item.value }))
    );

    const employeeName = employee.basic[2]?.value || `Employee_${index + 1}`;
    XLSX.utils.book_append_sheet(workbook, basicSheet, `${employeeName}_Basic`);
    XLSX.utils.book_append_sheet(workbook, paySheet, `${employeeName}_Pay`);
  });

  XLSX.writeFile(workbook, `Company_WageSlips_${period}.xlsx`);
};


  useEffect(() => {
    fetchData();
  }, [companyId, period]);

  if (isLoading) {
    return <PageLoader />;
  }

  if (!employees.length) {
    navigate(`/${companyId}/payroll/wage-register`)
    return <Error title="Could not load wage slips" />;
  }

  return (
    <>
      <header className="flex justify-center items-center mb-8">
        <div className="p-4 rounded-md shadow-md text-black font-bold text-center">
          <h3 className="text-lg font-bold">FORM XIX</h3>
          <p className="text-sm font-bold">See Rule 78(a)(i)</p>
          <p className="text-sm font-bold">Wage Slip</p>
        </div>
      </header>

      <div className="flex justify-end gap-4 mb-6">
          <div className="flex gap-4">
    <button
      className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
      onClick={onGenerateAllPDF}
    >
      Generate All PDF
    </button>
    <button
      className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
      onClick={onGenerateAllExcel}
    >
      Generate All Excel
    </button>
  </div>
        <DateFilter period={period} setPeriod={setPeriod} />
      </div>

      <div className="flex flex-col gap-8" ref={containerRef}>
        {employees.map((employee, index) => (
          <div 
            key={index} 
            className=" rounded-lg p-6 border border-gray-200 shadow-lg "
          >
            <div className=" top-4 right-4 flex gap-2 mb-3">
              <button
                className="text-white border border-white px-2.5 rounded hover:bg-green-600"
                onClick={() => onGeneratePDF(employee)}
              >
                Generate PDF
              </button>
              <button
                className="text-white border border-white px-2.5 rounded hover:bg-green-600"
                onClick={() => onGenerateExcel(employee)}
              >
                Generate Excel
              </button>
            </div>
            <div className="flex flex-col gap-5">
              <Table data={employee.basic} />
              <div className="flex items-end gap-5">
                <div className="min-w-[580px]">
                  <Table data={employee.pay} indexing={true} />
                </div>
                <p className="flex-1 text-gray-700 text-right break-all">
                  Initials of the Contractor or his Representative
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default CompanyWageSlips;