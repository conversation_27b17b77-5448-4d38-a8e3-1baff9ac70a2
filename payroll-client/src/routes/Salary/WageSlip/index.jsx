import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import salariesApi from "../../../apis/salaries";
import { getWageSlipData } from "../../../utils/salary";
import { generatePDF, generateExcel } from "../../../utils/export";
import PageLoader from "../../../components/PageLoader";
import Error from "../../../components/Error";
import Table from "./Table";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import "jspdf-autotable";

const WageSlip = () => {
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const [salary, setSalary] = useState();
  const { companyId } = useParams();

  const containerRef = useRef(null);

  const { salaryId } = useParams();

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const { data } = await salariesApi.fetchOne(salaryId);
      setSalary(getWageSlipData(data.salary));
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const onGeneratePDF = async () => {
    const doc = new jsPDF("p", "mm", "a4");
    const pageWidth = doc.internal.pageSize.width;
    const margin = 15;
  
    // Title section
    doc.setFontSize(14);
    doc.text("FORM XIX", pageWidth / 2, margin + 7, { align: "center" });
    doc.setFontSize(10);
    doc.text("See Rule 78(1)(b)", pageWidth / 2, margin + 14, { align: "center" });
    doc.setFontSize(12);
    doc.text("WAGE SLIP", pageWidth / 2, margin + 21, { align: "center" });
  
    // Updated Format currency values
    const formatValue = (value) => {
      if (typeof value === 'number') {
        return value.toString();
      }
      return value?.toString().replace(/[^0-9.,]/g, '') || value;
    };
  
    // Basic Details Table
    doc.autoTable({
      startY: margin + 30,
      body: salary.basic.map(item => [
        item.key, 
        typeof item.value === 'number' ? formatCurrency(item.value) : item.value
      ]),
      theme: 'plain',
      styles: {
        fontSize: 9,
        cellPadding: 3,
        overflow: 'linebreak'
      },
      columnStyles: {
        0: { 
          cellWidth: 70,
          fontStyle: 'bold'
        },
        1: { 
          cellWidth: 110,
          halign: 'left'  // Left align all values
        }
      },
      margin: { left: margin },
      didDrawCell: (data) => {
        doc.rect(data.cell.x, data.cell.y, data.cell.width, data.cell.height);
      }
    });
  
    // Pay Details Table
    doc.autoTable({
      startY: doc.lastAutoTable.finalY + 5,
      body: salary.pay.map(item => [
        item.key,
        typeof item.value === 'number' ? formatCurrency(item.value) : item.value
      ]),
      theme: 'plain',
      styles: {
        fontSize: 9,
        cellPadding: 3,
        overflow: 'linebreak'
      },
      columnStyles: {
        0: { 
          cellWidth: 70,
          fontStyle: 'bold'
        },
        1: { 
          cellWidth: 110,
          halign: 'left'  // Left align all values
        }
      },
      margin: { left: margin },
      didDrawCell: (data) => {
        doc.rect(data.cell.x, data.cell.y, data.cell.width, data.cell.height);
      }
    });
  
    // Signature and date
    doc.setFontSize(9);
    doc.text(
      "Initials of the Contractor or his Representative", 
      pageWidth - margin - 35, 
      doc.lastAutoTable.finalY + 15, 
      { align: "right" }
    );
  
    doc.text(
      `Date: ${new Date().toLocaleDateString('en-IN')}`,
      margin,
      doc.lastAutoTable.finalY + 15
    );
  
    doc.save("WageSlip.pdf");
  };
  

  const onGenerateExcel = () => {
    generateExcel(
      { title: "Employee Details", data: salary.basic },
      { title: "Wage Details", data: salary.pay }
    );
  };

  useEffect(() => {
    fetchData();
  }, [salaryId]);

  if (isLoading) {
    return <PageLoader />;
  }

  if (!salary) {
    navigate(`/${companyId}/payroll/wage-register`)

    return <Error title="Could not load wage slip" />;
  }

  return (
    <>
      
      <header className="flex justify-center items-center mb-8">
  <div className="p-4 rounded-md shadow-md text-black font-bold text-center">
    <h3 className="text-lg font-bold">FORM XIX</h3>
    <p className="text-sm font-bold">See Rule 78(a)(i)</p>
    <p className="text-sm font-bold">Wage Slip</p>
  </div>
</header>


            
        {/* <h2 className="flex-1 text-2xl font-semibold">WAGE SLIP</h2> */}
        <button
          className="text-white border border-white px-2.5 rounded hover:bg-green-600"
          onClick={onGeneratePDF}
        >
          Generate PDF
        </button>
        <button
          className="text-white border border-white px-2.5 rounded hover:bg-green-600"
          onClick={onGenerateExcel}
        >
          Generate Excel
        </button>
      {/* </header> */}

      <div className="flex flex-col gap-5" ref={containerRef}>
        <Table data={salary.basic} />
        <div className="flex items-end gap-5">
          <div className="min-w-[580px]">
            <Table data={salary.pay} indexing={true} />
          </div>
          <p className="flex-1 text-white text-right break-all">
            Initials of the Contractor or his Respresentative
          </p>
        </div>
      </div>
    </>
  );
};

export default WageSlip;
