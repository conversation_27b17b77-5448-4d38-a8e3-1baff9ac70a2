import { Route, Routes } from "react-router-dom";
import ClientManagementSidebar from "../../components/ClientManagementSidebar";
import All from "./All";
import WageSlip from "./WageSlip";

const Salary = () => {
  return (
    <div className="w-screen h-screen flex items-stretch">
      <ClientManagementSidebar />
      <main className="flex-1 bg-green-500 p-10 overflow-auto">
        <Routes>
          <Route path="" element={<All />} />
          <Route path=":salaryId" element={<WageSlip />} />
        </Routes>
      </main>
    </div>
  );
};

export default Salary;
