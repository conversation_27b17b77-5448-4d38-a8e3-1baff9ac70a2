export const getSheetData = (attendances, isCompanyView = false) => {
  if (isCompanyView) {
    // Handle company-wide view with client grouping
    return attendances.map(clientGroup => ({
      client: clientGroup.client,
      employees: clientGroup.employees.map(item => {
        const isLocked = item.salaries?.[0]?.net_amount != null;
        const advance = item.salaries?.[0]?.advance || 0;
        const mappedAttendances = (item.attendances || []).reduce(
          (acc, entry) => ({
            ...acc,
            [entry.date]: { id: entry.id, presence: entry.presence },
          }),
          {}
        );
        return {
          employee: {
            id: item.id,
            name: item.name,
            employee_id: item.employee_id,
            status: item.status,
            employee_created_at: item.employee_created_at
          },
          isLocked,
          advance,
          attendances: mappedAttendances,
        };
      })
    }));
  }

  // Original logic for single client view
  return attendances.map((item) => {
    const { attendances, salaries, ...employee } = item;
    const isLocked = salaries?.[0]?.net_amount != null;
    const advance = salaries?.[0]?.advance || 0;
    const mappedAttendances = attendances.reduce(
      (acc, entry) => ({
        ...acc,
        [entry.date]: { id: entry.id, presence: entry.presence },
      }),
      {}
    );
    return {
      employee,
      isLocked,
      advance,
      attendances: mappedAttendances,
    };
  });
};
