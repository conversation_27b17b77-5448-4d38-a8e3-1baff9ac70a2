export const getYearOptions = () => {
  const currentYear = new Date().getFullYear();
  return Array.from({ length: currentYear - 2000 + 1 }, (_, index) => ({
    label: 2000 + index,
    value: 2000 + index,
  }));
};

export const getMonthOptions = (year) => {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  const allMonths = [
    { value: 1, label: "January" },
    { value: 2, label: "February" },
    { value: 3, label: "March" },
    { value: 4, label: "April" },
    { value: 5, label: "May" },
    { value: 6, label: "June" },
    { value: 7, label: "July" },
    { value: 8, label: "August" },
    { value: 9, label: "September" },
    { value: 10, label: "October" },
    { value: 11, label: "November" },
    { value: 12, label: "December" },
  ];
  if (year === currentYear) {
    return allMonths.filter((month) => month.value <= currentMonth);
  }
  return allMonths;
};

export const getCurrentYearMonth = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  return parseInt(`${year}${month}`);
};

export const getCurrentYearMonthDay = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate() + 1).padStart(2, "0");
  return parseInt(`${year}${month}${day}`);
};

export const getDates = (year, month) => {
  const daysInMonth = new Date(year, month, 0).getDate();
  const days = [];
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day);
    // Weekend = [0: Sunday, 6: Saturday]
    const isWeekend = date.getDay() === 0 || date.getDay() === 6;
    const isSunday = date.getDay() === 0;
    days.push({
      date: `${year}${month.toString().padStart(2, "0")}${day
        .toString()
        .padStart(2, "0")}`,
      isWeekend,
      isSunday
    });
  }
  return days;
};

export const getStringFromPeriod = (period) => {
  const year = Math.floor(period / 100);
  const month = period % 100;
  const date = new Date(year, month - 1);
  return date.toLocaleString("en-US", {
    month: "long",
    year: "numeric",
  });
};



export const formatDate = (dateString) => {
  const date = new Date(dateString);
  
  const day = String(date.getDate()).padStart(2, '0');  // Get day and pad with zero if needed
  const month = String(date.getMonth() + 1).padStart(2, '0');  // Get month (0-based, so add 1) and pad
  const year = date.getFullYear();  // Get full year

  return `${day}-${month}-${year}`;
};
