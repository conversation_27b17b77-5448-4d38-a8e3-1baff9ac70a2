import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import * as XLSX from "xlsx";

export const generatePDF = (container) => {
  html2canvas(container, {
    scale: 2, // Increase scale to improve image quality
    logging: false,
    useCORS: true,
    scrollX: 0,
    scrollY: -window.scrollY, // Handle any page offset
  }).then((canvas) => {
    const imgData = canvas.toDataURL("image/png");
    const doc = new jsPDF("p", "mm", "a4");
    const imgWidth = 200; // A4 width in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    // Add image to PDF, adjusting the height to avoid cropping
    doc.addImage(imgData, "PNG", 10, 10, imgWidth, imgHeight);

    // If the content is large, we handle the overflow to next pages
    if (imgHeight > 280) {
      // A4 height limit is 297mm, 280mm leaves space for margins
      let totalPages = Math.ceil(imgHeight / 280);
      for (let i = 1; i < totalPages; i++) {
        doc.addPage();
        doc.addImage(imgData, "PNG", 10, -280 * i, imgWidth, imgHeight);
      }
    }
    doc.save("wage-slip.pdf");
  });
};

export const generateExcel = (...dataSections) => {
  console.log(dataSections);
  const workbook = XLSX.utils.book_new();

  dataSections.forEach((section) => {
    const sheetData = section.data?.map(({ key, value }) => ({
      Label: key,
      Value: value,
    }));
    const sheet = XLSX.utils.json_to_sheet(sheetData);
    XLSX.utils.book_append_sheet(workbook, sheet, section.title);
  });

  XLSX.writeFile(workbook, "wage-slip.xlsx");
};
