export const getWagesRegisterData = (data) => {
  return data?.map((item, idx) => {
    const { salaries, ...employee } = item;
    const salary = salaries?.[0] || {};
    return { idx, employee, salary };
  });
};

export const getWageSlipData = (data) => {
  // TODO: Add fields as required
  const basic = [
    {
      key: "NAME AND ADDRESS OF CONTRACTOR",
      value:
       `${data.employee.client.name}, ${data.employee.client.address}`,
    },
    {
      key: "Nature and location of work",
      value: `${data.employee.client.contract_category}, ${data.employee.client.name}`,
    },
    {
      key: "Name of the employee",
      value: data.employee.name,
    },
    {
      key: "Staff ID #",
      value: data.employee.employee_id,
    },
    {
      key: "Salary Period",
      value: data.period,
    },
    {
      key: "Name and <PERSON>'s / Hu<PERSON>'s name of the workman",
      value: "",
    },
    {
      key: "For the Week / Fortnight / Month ending",
      value: "",
    },
  ];
  const pay = [
    {
      key: "1. No. of days worked",
      value: data.days_worked,
    },
    {
      key: "2. No. of units worked in case of piecerate workers",
      value: "",
    },
    {
      key: "3. Rate of daily wages / piece rate",
      value: "",
    },
    {
      key: "4. Amount of overtime wages",
      value: "",
    },
    {
      key: "5. Gross wages payable",
      value: `₹ ${data.gross_amount || 0}`,
    },
    {
      key: "6. Deductions, if any",
      value: `₹ ${data.epf || 0 + data.esi || 0}`,
    },
    {
      key: "7. Advance",
      value: `₹ ${data.advance || 0}`,
    },
    {
      key: "8. Net amount of wages paid",
      value: `₹ ${(data.net_amount || 0).toFixed(2)}`,
    },
  ];
  return { basic, pay };
};




