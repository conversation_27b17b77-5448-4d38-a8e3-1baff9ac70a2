# packages
node_modules
jspm_packages

# Logs
logs
*.log
npm-debug.log*

# OS caches
**/.DS_Store

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# env variables
.env

# docker
docker-compose.override.yaml
docker-compose.override.yml
