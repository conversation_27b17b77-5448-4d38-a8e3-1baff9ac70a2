"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("Employees", "pan_number", {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn("Employees", "branch", {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn("Employees", "cmp_code", {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("Employees", "pan_number");
    await queryInterface.removeColumn("Employees", "branch");
    await queryInterface.removeColumn("Employees", "cmp_code");
  },
};
