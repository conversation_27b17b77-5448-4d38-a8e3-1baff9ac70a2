"use strict";
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface
      .removeIndex("Salaries", "unique_salary_combination")
      .catch(() => {});

    await queryInterface.createTable("Salaries", {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      period: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      days_worked: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      daily_rate: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      gross_amount: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      net_amount: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      advance: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      epf: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      esi: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      employee_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: { model: "Employees", key: "id" },
        onDelete: "RESTRICT",
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex("Salaries", {
      type: "unique",
      fields: ["employee_id", "period"],
      name: "unique_salary_combination",
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface
      .removeIndex("Salaries", "unique_salary_combination")
      .catch(() => {});
    await queryInterface.dropTable("Salaries");
  },
};
