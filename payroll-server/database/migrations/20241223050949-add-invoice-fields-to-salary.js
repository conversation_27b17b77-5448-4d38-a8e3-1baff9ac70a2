"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("Salaries", "epf_employer", {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn("Salaries", "esi_employer", {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn("Salaries", "actual_gross_amount", {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn("Salaries", "service_charge", {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn("Salaries", "billable_amount", {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("Salaries", "epf_employer");
    await queryInterface.removeColumn("Salaries", "esi_employer");
    await queryInterface.removeColumn("Salaries", "service_charge");
    await queryInterface.removeColumn("Salaries", "billable_amount");
  },
};
