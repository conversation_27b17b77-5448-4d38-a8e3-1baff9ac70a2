"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("Salaries", "reason_code", {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: null, // default null
    });
    await queryInterface.addColumn("Salaries", "last_working_day", {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null, 
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("Salaries", "reason_code");
    await queryInterface.removeColumn("Salaries", "last_working_day");
  },
};
