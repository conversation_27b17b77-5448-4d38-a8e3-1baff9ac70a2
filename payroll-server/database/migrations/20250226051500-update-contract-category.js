"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    // Step 1: Remove the old ENUM column
    await queryInterface.removeColumn("Clients", "contract_category");

    // Step 2: Add a new ENUM column with updated values
    await queryInterface.addColumn("Clients", "contract_category", {
      type: Sequelize.ENUM(
        "Cleaning Outcome Based",
        "Manpower Outsourcing",
        "Facility Outcome Based"
      ),
      allowNull: true,
    });

    // Step 3: Update existing records
    await queryInterface.sequelize.query(`
      UPDATE "Clients" SET contract_category = 'Cleaning Outcome Based' WHERE contract_category = 'Cleaning';
    `);

    await queryInterface.sequelize.query(`
      UPDATE "Clients" SET contract_category = 'Manpower Outsourcing' WHERE contract_category = 'Manpower';
    `);
  },

  async down(queryInterface, Sequelize) {
    // Step 1: Remove the updated ENUM column
    await queryInterface.removeColumn("Clients", "contract_category");

    // Step 2: Restore the old ENUM column
    await queryInterface.addColumn("Clients", "contract_category", {
      type: Sequelize.ENUM("Cleaning", "Manpower"),
      allowNull: true,
    });

    // Step 3: Revert the data updates
    await queryInterface.sequelize.query(`
      UPDATE "Clients" SET contract_category = 'Cleaning' WHERE contract_category = 'Cleaning Outcome Based';
    `);

    await queryInterface.sequelize.query(`
      UPDATE "Clients" SET contract_category = 'Manpower' WHERE contract_category = 'Manpower Outsourcing';
    `);
  },
};
