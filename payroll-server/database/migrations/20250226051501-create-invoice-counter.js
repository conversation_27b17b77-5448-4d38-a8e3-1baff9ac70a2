"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("InvoiceCounters", {
      id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
      },
      client_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: "Clients",
          key: "id",
        },
        onDelete: "CASCADE",
      },
      period: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "Format: YYYYMM",
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Add unique constraint for client_id and period combination
    await queryInterface.addIndex("InvoiceCounters", {
      type: "unique",
      fields: ["client_id", "period"],
      name: "unique_invoice_counter_combination",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface
      .removeIndex("InvoiceCounters", "unique_invoice_counter_combination")
      .catch(() => {});

    await queryInterface.dropTable("InvoiceCounters");
  },
};
