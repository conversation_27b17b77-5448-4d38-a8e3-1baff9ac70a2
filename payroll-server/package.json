{"name": "payroll-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "express": "^4.21.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "sequelize": "^6.37.5"}, "devDependencies": {"nodemon": "^3.1.7", "sequelize-cli": "^6.6.2"}}