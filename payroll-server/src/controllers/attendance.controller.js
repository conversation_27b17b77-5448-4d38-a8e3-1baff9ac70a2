const { Op } = require("sequelize");
const { Attendance, Employee, <PERSON><PERSON> ,Client} = require("../models");

const getAll = async (req, res) => {
  const { clientId, period } = req.query;
  if (!clientId) {
    return res.status(400).json({ error: "clientId is required" });
  }

  const attendanceConditions = {
    date: { [Op.between]: [Number(period) * 100, Number(period) * 100 + 31] },
  };
  const salaryConditions = { period: Number(period) };
  try {
    console.log("Client ID:", clientId); 
    const attendances = await Employee.findAll({
      where: { client_id: clientId, status:"ACTIVE" },
      attributes: ["id", "name", "employee_id", "status","contribution",  ["createdAt", "employee_created_at"],],
      include: [
        {
          model: Attendance,
          as: "attendances",
          where: attendanceConditions,
          required: false,
        },
        {
          model: Salary,
          as: "salaries",
          where: salaryConditions,
          required: false,
        },
      ],
    });
    

    return res.json({ attendances });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};


const getCompanyAttendance = async (req, res) => {
  const { companyId, period } = req.query;

  if (!companyId) {
    return res.status(400).json({ error: "companyId is required" });
  }

  const attendanceConditions = {
    date: { [Op.between]: [Number(period) * 100, Number(period) * 100 + 31] },
  };
  const salaryConditions = { period: Number(period) };

  try {
    // First get all clients for the company
    const clients = await Client.findAll({
      where: { company_id: companyId },
      attributes: [
        'id', 
        'name', 
        'address',
        'gst',
        'tan',
        'contract_number',
        'contract_value',
        'contract_category',
        'contract_duration',
        'days'
      ],
      include: [{
        model: Employee,
        as: 'employees',
        where: { status: "ACTIVE" },
        attributes: [
          "id",
          "name",
          "employee_id",
          "status",
          ["createdAt", "employee_created_at"]
        ],
        include: [
          {
            model: Attendance,
            as: "attendances",
            where: attendanceConditions,
            required: false
          },
          {
            model: Salary,
            as: "salaries",
            where: salaryConditions,
            required: false
          }
        ]
      }]
    });

    // Transform the data to match the salary controller format
    const attendances = clients.map(client => ({
      client: {
        id: client.id,
        name: client.name,
        address: client.address,
        gst: client.gst,
        tan: client.tan,
        contract_number: client.contract_number,
        contract_value: client.contract_value,
        contract_category: client.contract_category,
        contract_duration: client.contract_duration,
        days: client.days
      },
      employees: client.employees.map(employee => ({
        id: employee.id,
        name: employee.name,
        employee_id: employee.employee_id,
        status: employee.status,
        employee_created_at: employee.employee_created_at,
        attendances: employee.attendances || [],
        salaries: employee.salaries || []
      }))
    }));

    return res.json({ attendances });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};


const createOne = async (req, res) => {
  try {
    const { client_id, employee_id, presence, date } = req.body;

    // First check if attendance already exists
    const existingAttendance = await Attendance.findOne({
      where: {
        employee_id,
        client_id,
        date
      }
    });

    if (existingAttendance) {
      // If it exists, update it instead of creating new
      await existingAttendance.update({ presence });
      return res.json(existingAttendance);
    }

    // If it doesn't exist, create new
    const attendance = await Attendance.create({
      client_id,
      employee_id,
      presence,
      date
    });

    res.json(attendance);
  } catch (error) {
    console.error('Error creating attendance:', error);
    res.status(500).json({ error: error.message });
  }
};

const updateOne = async (req, res) => {
  const { id } = req.params;
  try {
    const attendance = await Attendance.update(
      { ...req.body },
      { where: { id } }
    );
    res.status(200).json({ attendance });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

module.exports = { getAll, createOne, updateOne,getCompanyAttendance };
