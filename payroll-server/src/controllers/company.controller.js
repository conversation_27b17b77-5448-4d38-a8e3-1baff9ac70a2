const { Company } = require("../models");

const getAll = async (req, res) => {
  try {
    const companies = await Company.findAll({ where: { user_id: req.userId } });
    res.json({ companies });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};


const getOne = async (req, res) => {
  const { id } = req.params;
  try {
    const company = await Company.findByPk(id);
    res.json({ company });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

module.exports = { getAll, getOne };



const dummyData = {
  id: "123e4567-e89b-12d3-a456-************", 
  name: "IFM Pvt Ltd",
  logo_url: "https://res.cloudinary.com/djvkse7a8/image/upload/v1735629893/srgjzy0nftt7dtrudo16.jpg", 
  user_id: "bf431662-dea0-4b42-8f18-74e0a7a02ec8", 
  bank_account_number: "***************",
  ifsc_number: "UTIB0000148",
  address_1: "TP Complex, Nr. Municipal Office",
  address_2: "Talaparamba, Kannur",
  pin: "670002",
  street: "Kannur Road",
  district: "Kannur",
  state: "Kerala",
  gstin: "32AAAFI8279M1ZA",
  pan: "**********",
  branch: "SMRoad",
  bank_name: "State Bank of India",
  gst_number: "32AAAFI8279M1ZA",
  email:"",
  contact_number:"",
  secondary_email:""
};