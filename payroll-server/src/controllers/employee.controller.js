const { Employee,<PERSON><PERSON> } = require("../models");

const getAll = async (req, res) => {
  const { status, client_id } = req.query;
  try {
    const employees = await Employee.findAll({
      where: { client_id, status: status || "ACTIVE" },
      attributes: ["id", "name", "employee_id", "mobile_number","daily_wage"],
    });
    res.json({ employees });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

const getOne = async (req, res) => {
  const { id } = req.params;
  try {
    const employee = await Employee.findByPk(id);
    res.json({ employee });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

const createOne = async (req, res) => {
  try {
    const employee = await Employee.create({ ...req.body });
    res.status(201).json({ employee });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

const updateOne = async (req, res) => { 
  const { id } = req.params; 
  try { 
    const currEmployee = await Employee.findByPk(id); 
    if (!currEmployee) { 
      return res.status(404).json({ error: "Employee not found" }); 
    } 
 
    const oldContribution = Number(currEmployee.contribution); 
    
    const newContribution = Number(req.body?.contribution); 
     
 
    await Employee.update({ ...req.body }, { where: { id } }); 
 
    if ( 
      req.body?.contribution !== undefined && 
      oldContribution !== newContribution 
    ) { 
      
      const salaryDetails = await Salary.findAll({ 
        where: { 
          employee_id: id, 
          net_amount: null, 
        }, 
      }); 
       
      const salaryUpdateDetails = await Salary.update( 
        { advance: newContribution }, 
        { 
          where: { 
            employee_id: id, 
            net_amount: null, 
          }, 
        } 
      ); 
       
    } 
 
    res.status(200).json({ message: "Employee updated successfully" }); 
  } catch (err) { 
    console.error(err); 
    res.status(500).json({ error: "Internal server error" }); 
  } 
};

const changeStatus = async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  try {
    const employee = await Employee.update({ status }, { where: { id } });
    if (employee) {
      res.status(200).json({ employee });
    } else {
      res.status(404).json({ error: "Employee not found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

module.exports = { getAll, getOne, createOne, updateOne, changeStatus };
