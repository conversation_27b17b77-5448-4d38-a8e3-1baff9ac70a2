const { Op } = require("sequelize");
const { Employee, Salary, Attendance, Client, Company, InvoiceCounter } = require("../models");
const {Sequelize} = require("sequelize") ;
const { startOfMonth : funcStartOfMonth, endOfMonth : funcEndOfMonth, eachDayOfInterval, format } = require("date-fns");

const {
  generateSalaryRecordData,
  getCombinedInvoiceData,
} = require("../utils/salary.utils");

const getTotalDaysInMonth = (yyyymm) => {
  const year = parseInt(yyyymm.substring(0, 4), 10);
  const month = parseInt(yyyymm.substring(4, 6), 10);

  // Get the number of days in the month
  const totalDays = new Date(year, month, 0).getDate();

  return totalDays;
};


const getAll = async (req, res) => {
  const { clientId, period } = req.query;
  try {
    const salaries = await Employee.findAll({
      where: { client_id: clientId, status:"ACTIVE" },
      attributes: ["id", "name", "employee_id", "status","allowance","bonus"],
      include: [
        {
          model: Salary,
          as: "salaries",
          where: { period },
          required: false,
        },
      ],
    });
    return res.status(200).json({ salaries });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

const getCompanyAll = async (req, res) => {
  const { companyId, period } = req.query;
  try {
    const clients = await Client.findAll({
      where: { company_id: companyId },
      attributes: [
        'id', 
        'name', 
        'address', 
        'gst',
        'tan',
        'contract_number',
        'contract_value',
        'contract_category',
        'contract_duration',
        'days'
      ],
      include: [{
        model: Employee,
        as: 'employees',
        where: { status: "ACTIVE" },
        attributes: [
          "id", 
          "name", 
          "employee_id", 
          "status",
          "epf_uan",
          "esi_number",
          "bank_account_number",
          "ifsc",
          "bank_name","allowance","bonus"
        ],
        include: [{
          model: Salary,
          as: "salaries",
          where: { period },
          required: false,
          attributes: [
            "days_worked",
            "id",
            "daily_rate",
            "gross_amount",
            "net_amount",
            "advance",
            "epf",
            "esi",
            "epf_employer",
            "esi_employer",
            "actual_gross_amount",
            "service_charge",
            "billable_amount"
          ]
        }],
      }],
    });


    const clientWiseSalaries = clients.map(client => ({
      client: {
        id: client.id,
        name: client.name,
        address: client.address,
        gst: client.gst,
        tan: client.tan,
        contract_number: client.contract_number,
        contract_value: client.contract_value,
        contract_category: client.contract_category,
        contract_duration: client.contract_duration,
        days: client.days
      },
      employees: client.employees
    }));
    return res.status(200).json({ salaries:clientWiseSalaries });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

const getOne = async (req, res) => {
  const { id } = req.params;
  try {
    const salary = await Salary.findOne({
      where: { id },
      include: {
        model: Employee,
        as: "employee",
        required: true,
        include: [{ model: Client, as: "client", required: true }],
      },
    });
    return res.status(200).json({ salary });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

const getInvoice = async (req, res) => {
  const { clientId, period } = req.query;
  try {
    const client = await Client.findOne({ where: { id: clientId } });
    const salaries = await Salary.findAll({
      where: { period },
      include: [
        {
          model: Employee,
          as: "employee",
          where: { client_id: clientId, status:"ACTIVE" },
          required: true,
          include: [
            {
              model: Client,
              as: "client",
            },
          ],
        },
      ],
    });

    if (!salaries.length) {
      return res.status(404).json({
        error: "No active employees found for this client and period.",
      });
    }

    const [invoiceCounter] = await InvoiceCounter.findOrCreate({
      where: { client_id: clientId, period },
    });

    const invoice = {
      period,
      client,
      invoice_number: invoiceCounter.id,
      details: getCombinedInvoiceData(salaries, client.gst !=="0"),
    };
    return res.status(200).json({ invoice });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

const addAdvance = async (req, res) => {
  const { employee_id, period, advance } = req.body;
  try {
    const employee = await Employee.findOne({
      where: { id: employee_id },
      include: [
        {
          model: Salary,
          as: "salaries",
          where: { period },
          required: false,
        },
      ],
    });
   

    if (!employee) {
      return res.status(404).json({ error: "Employee not found" });
    }
    if (employee.salaries?.[0]?.net_amount != null) {
      return res.status(403).json({ error: "Salary already generated" });
    }
    if (advance < 0 || advance > employee.daily_wage * 10) {
      return res
        .status(403)
        .json({ error: "Advance should be between 0 and 10*(daily wage) " });
    }
    await Salary.upsert({ employee_id, period, advance });
    return res.status(200).json({});
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

const generate = async (req, res) => { 
  const { employee_id, period } = req.body; 

// console.log("🚀 ~ generate ~ period:", period); 

  try { 
    const employee = await Employee.findOne({ 
      where: { id: employee_id }, 
      include: [ 
        { 
          model: Salary, 
          as: "salaries", 
          where: { period }, 
          required: false, 
        }, 
        { 
          model: Attendance, 
          as: "attendances", 
          where: { 
            date: { 
              [Op.between]: [Number(period) * 100, Number(period) * 100 + 31], 
            }, 
          }, 
          required: false, 
        }, 
        { 
          model: Client, 
          as: "client", 
          attributes: [ 
            "contract_category", 
            "contract_value", 
            "contract_duration", 
            "contract_start_date", 
            "contract_expiry_date", 
          ], 
          required: true, 
        }, 
      ], 
    }); 
 
    if (!employee) { 
      return res.status(404).json({ error: "Employee not found" }); 
    } 
 
    // console.log( 
    //   "🚀 ~ generate ~ employee.salaries?.[0]:", 
    //   employee.salaries?.[0] 
    // ); 
    if (employee.salaries?.[0]?.net_amount != null) { 
      return res.status(400).json({ error: "Salary already generated once" }); 
    } 
 
    // ✨ Check if Contract is Expired ✨ 
    // ✨ Contract Expiry Date Validation ✨ 
    const contractExpiryDate = employee.client.contract_expiry_date; 
    // console.log("🚀 ~ generate ~ contractExpiryDate:", contractExpiryDate); 
    if (contractExpiryDate) { 
      const [year, month, day] = contractExpiryDate.split("-").map(Number); 
      const expiry = new Date(year, month - 1, day); // Correctly parse YYYY-MM-DD 
      // console.log("🚀 ~ generate ~ expiry:", expiry); 
 
      const periodYear = Number(period.toString().slice(0, 4)); // e.g. 202404 -> 2024 
      // console.log("🚀 ~ generate ~ periodYear:", periodYear); 
      const periodMonth = Number(period.toString().slice(4)); // e.g. 202404 -> 04 
      // console.log("🚀 ~ generate ~ periodMonth:", periodMonth); 
      const salaryPeriodDate = new Date(periodYear, periodMonth - 1, 1); // 1st of month 
      // console.log("🚀 ~ generate ~ salaryPeriodDate:", salaryPeriodDate); 
 
      if (salaryPeriodDate > expiry) { 
        // console.log("salary period date in greater than expire date"); 
        return res 
          .status(400) 
          .json({ error: "Contract has expired. Cannot generate salary." }); 
      } 
    } 
 
    const [record, _] = await Salary.upsert( 
      generateSalaryRecordData(employee, period), 
      { returning: true } 
    ); 
 
    return res.status(201).json({ salary: record.toJSON() }); 
  } catch (err) { 
    // console.error(err); 
    res.status(500).json({ error: "Internal server error" }); 
  } 
};







const getSalariesByPeriod = async (req, res) => {
  const { period,company } = req.params;

  const currentPeriod = period || (() => {
    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const year = now.getFullYear();
    return `${year}${month}`;
  })();

  const startOfMonth = parseInt(currentPeriod + "01", 10); 
  const endOfMonth = parseInt(currentPeriod + "31", 10); 

  try {
    if (!currentPeriod || currentPeriod === "undefined") {
      return res.status(400).json({ error: "Invalid period" });
    }


    const attendanceData = await Attendance.findAll({
      where: {
        date: {
          [Op.gte]: startOfMonth,
          [Op.lte]: endOfMonth,
        },
      },
      attributes: [
        "employee_id",
        [
          Sequelize.fn(
            "SUM",
            Sequelize.literal("CASE WHEN presence = 0 THEN 1 ELSE 0 END")
          ),
          "total_absence",
        ],
        [
          Sequelize.fn(
            "SUM",
            Sequelize.literal("CASE WHEN presence = 1 THEN 1 ELSE 0 END")
          ),
          "total_present",
        ],
        [
          Sequelize.fn("COUNT", Sequelize.fn("DISTINCT", Sequelize.col("date"))),
          "total_working_days",
        ],
      ],
      group: ["employee_id"],
    });
 
    const attendanceMap = attendanceData.reduce((map, attendance) => {
      const { employee_id, total_absence ,total_present} = attendance.dataValues;
      map[employee_id] = {total_present,total_absence};
      return map;
    }, {});

    const totalDays = getTotalDaysInMonth(period)

    const salaries = await Salary.findAll({
      where: { period: currentPeriod },
      include: [
        {
          model: Employee,
          as: "employee",
          attributes: [
            "id",
            "name",
            "bank_account_number",
            "cmp_code",
            "ifsc",
            "daily_wage",
            "esi_number",
            "contribution",
            "epf_uan",
            "bonus",
            "allowance",
            "employee_id",
          
          ],
          where: { status:'ACTIVE'},
          include: [
            {
              model: Client,
              as: "client",
              attributes: ["client_code"], 
              where: { company_id: company }, 
            },
          ],
        },
      ],
      attributes: [
        "id",
        "gross_amount",
        "net_amount",
        "advance",
        "epf",
        "esi",
        "last_working_day",
        "reason_code",
      ],
    });
    console.log(salaries)
    
    
        const enrichedSalaries = salaries
      .filter((salary) => salary.employee !== null) 
      .map((salary) => {
        const employeeId = salary.employee?.id; 
        return {
          ...salary.toJSON(),
          absent_days: (totalDays - Number(attendanceMap[employeeId].total_present))|| 0, 
          present_days:attendanceMap[employeeId].total_present || 0, 
          net_amount : (salary.net_amount)
        };
      });


    if (!enrichedSalaries.length) {
      return res
        .status(404)
        .json({ error: `No salaries found for period ${currentPeriod}` });
    }

    return res.status(200).json({ salaries: enrichedSalaries });
  } catch (err) {
    console.error("Error fetching salaries:", err);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const updateSalaryData = async (req, res) => {
  const { id } = req.params;
  const { reason_code, last_working_day } = req.body;

  try {
    const salary = await Salary.findByPk(id);
    if (!salary) {
      return res.status(404).json({ error: "Salary record not found" });
    }

    salary.reason_code = reason_code || salary.reason_code;
    salary.last_working_day = last_working_day || salary.last_working_day;
    await salary.save();

    return res.status(200).json({ message: "Salary updated successfully", salary });
  } catch (err) {
    console.error("Error updating salary:", err);
    return res.status(500).json({ error: "Internal server error" });
  }
};


const updateEpfData = async (req, res) => {
  const { id } = req.params;
  const { gross_amount } = req.body;

  try {
    const salary = await Salary.findByPk(id);
    if (!salary) {
      return res.status(404).json({ error: "Salary record not found" });
    }

    if (gross_amount !== undefined && gross_amount !== salary.gross_amount) {
      const previousGrossAmount = salary.gross_amount;
      
      salary.gross_amount = Number(gross_amount);

      const netAmountDifference = salary.gross_amount - previousGrossAmount;
      
      salary.net_amount += netAmountDifference;

      await salary.save(); 

      return res.status(200).json({ message: "Salary updated successfully", salary });
    }

    return res.status(400).json({ error: "Gross amount is either missing or unchanged" });
  } catch (err) {
    console.error("Error updating salary:", err);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const getCompanySalaryDataByPeriod = async (req, res) => {
  const { companyId, period } = req.params;
  try {
    const employees = await Employee.findAll({
      where:{ status:'ACTIVE'},
      include: [
        {
          model: Client,
          as: "client",
          where: { company_id: companyId },
          required: true,
          attributes: ["name", "contract_category", "contract_value","address"],
        },
        {
          model: Salary,
          as: "salaries",
          where: { period },
          required: false,
          attributes: [
            "id",
            "days_worked",
            "daily_rate",
            "gross_amount",
            "net_amount",
            "advance",
            "epf",
            "esi",
            "epf_employer",
            "esi_employer",
            "actual_gross_amount",
            "service_charge",
            "billable_amount",
            "reason_code",
            "last_working_day"
          ]
        }
      ],
      attributes: [
        "id",
        "name",
        "employee_id",
        "epf_uan",
        "esi_number",
        "bank_account_number",
        "ifsc",
        "bank_name",
        "cmp_code",
        "daily_wage",
        "status"
      ]
    });

    if (!employees.length) {
      return res.status(404).json({ 
        error: `No employees found for company ID ${companyId} in period ${period} `
      });
    }

    return res.status(200).json({ employees });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};

module.exports = { 
  getAll, 
  getOne, 
  getInvoice, 
  addAdvance, 
  generate,
  getSalariesByPeriod,
  updateSalaryData,
  updateEpfData,
  getCompanyAll,
  getCompanySalaryDataByPeriod  // Add this line
};