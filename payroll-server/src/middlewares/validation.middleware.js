const validatePayload = (schema) => {
  return (req, res, next) => {
    const options = { abortEarly: false, stripUnknown: true };
    const { error, value } = schema.validate(req.body, options);

    if (error) {
      console.log(error)
      const errors = error.details.reduce((acc, detail) => {
        const field = detail.context.key;
        if (!acc[field]) acc[field] = [];
        acc[field].push(detail.message);
        return acc;
      }, {});

      return res.status(400).json({
        error: "Invalid request payload",
        details: errors,
      });
    }

    req.body = value;
    next();
  };
};

module.exports = { validatePayload };
