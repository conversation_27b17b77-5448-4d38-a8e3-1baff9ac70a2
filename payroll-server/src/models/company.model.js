const { DataTypes } = require("sequelize");
const database = require("../configs/connection");
const User = require("./user.model");

const Company = database.define("Company", {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  logo_url: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  user_id: {
    type: DataTypes.UUID,
    references: { model: User, key: "id" },
  },
  bank_account_number: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  ifsc_number: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  address_1: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  address_2: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  pin: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  street: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  district: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  state: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  gstin: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  pan: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  branch: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  bank_name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  gst_number: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  secondary_email: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  contact_number: {
    type: DataTypes.STRING,
    allowNull: true,
  },
});





module.exports = Company;
