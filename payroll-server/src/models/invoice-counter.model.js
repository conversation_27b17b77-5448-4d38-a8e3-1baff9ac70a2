const { DataTypes } = require("sequelize");
const sequelize = require("../configs/connection");
const Client = require("./client.model");

const InvoiceCounter = sequelize.define(
  "InvoiceCounters",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    client_id: {
      type: DataTypes.UUID,
      references: { model: Client, key: "id" },
      allowNull: false,
    },
    period: {
      type: DataTypes.STRING, // Format: YYYYMM
      allowNull: false,
    },
  },
  {
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ["client_id", "period"],
        name: "unique_invoice_counter_combination",
      },
    ],
  }
);

module.exports = InvoiceCounter;
