const { DataTypes } = require("sequelize");
const database = require("../configs/connection");
const Employee = require("./employee.model");

const Salary = database.define(
  "Salary",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    period: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    days_worked: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    daily_rate: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    gross_amount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    net_amount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    advance: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    epf: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    esi: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    employee_id: {
      type: DataTypes.UUID,
      references: { model: Employee, key: "id" },
      allowNull: false,
    },
    epf_employer: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    esi_employer: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    actual_gross_amount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    service_charge: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    billable_amount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    reason_code: {
      type: DataTypes.STRING,
      allowNull: true,  
      defaultValue: null, 
    },
    last_working_day: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: null, 
    }
  },
  {
    indexes: [
      {
        unique: true,
        fields: ["employee_id", "period"],
        name: "unique_salary_combination",
      },
    ],
  }
);


// const ok = async() => {
//   await Salary.drop()
// }
// ok()
module.exports = Salary;
