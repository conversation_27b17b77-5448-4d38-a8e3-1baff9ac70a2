const express = require("express");
const { authenticateToken } = require("../middlewares/auth.middleware");
const controller = require("../controllers/salary.controller");

const router = express.Router();

router.use(authenticateToken);

router.get("/", controller.getAll);
router.get("/company", controller.getCompanyAll);

router.get("/invoice", controller.getInvoice);
router.get("/:id", controller.getOne);
router.get("/:companyId/:period", controller.getCompanySalaryDataByPeriod);
router.post("/advance", controller.addAdvance);
router.post("/generate", controller.generate);
router.get("/data/:company/:period?", controller.getSalariesByPeriod);
router.patch("/salaries/:id?", controller.updateSalaryData);
router.patch("/epf/:id?", controller.updateEpfData);





module.exports = router;
