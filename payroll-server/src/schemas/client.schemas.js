const Joi = require("joi");

const createSchema = Joi.object({
  name: Joi.string().required(),
  address: Joi.string().min(8).required(),
  gst: Joi.string().allow("").optional(),
  tan: Joi.string().allow("").optional(),
  contract_number: Joi.string().required(),
  contract_value: Joi.string().required(),
  contract_category: Joi.string().valid('Cleaning Outcome Based', 'Manpower Outsourcing', 'Facility Outcome Based').required(),
  contract_duration: Joi.string().required(),
  days: Joi.number().required(),
  epbg_amount: Joi.string().required(),
  epbg_expiry_date: Joi.string(),
  contract_start_date: Joi.string().required(),
  contract_expiry_date: Joi.string().required(),
  epbg_date: Joi.string().optional(),
  epbg_number: Joi.string(),
  client_code: Joi.string(),
// Foreign key reference to company
  company_id: Joi.string().guid().required(), 
});

module.exports = { createSchema };
