const DEDUCTION_LIMIT = 15000;
const DEDUCTION_LIMIT_ESI = 21000;

const EPF_PERCENT = 12;
const ESI_PERCENT = 0.75;
const EPF_EMPLOYER_PERCENT = 13;
const ESI_EMPLOYER_PERCENT = 3.25;
const SERVICE_CHARGE_PERCENT = 3.26;
const CGST_PERCENT = 9;
const SGST_PERCENT = 9;


const toNumber = (value) => {
  const num = Number(String(value || "").trim().replace(/,/g, ""));
  return isNaN(num) ? 0 : num;
};


const generateSalaryRecordData = (employee, period) => {
  const data = { period };
  const days_worked =  employee.attendances.reduce((acc, entry) => {
    return acc + entry.presence;
  }, 0);
  data.employee_id = employee.id;
  data.days_worked =days_worked
  data.daily_rate = Number(employee.daily_wage);


  data.gross_amount = data.days_worked * data.daily_rate;
  data.advance = employee.salaries?.[0]?.advance ?? 0;
  data.epf = employee.epf_uan == "0" ? 0 :(Math.min(data.gross_amount, DEDUCTION_LIMIT) * EPF_PERCENT) / 100;
  data.esi = employee.esi_number == "0" ? 0 :(Math.min(data.gross_amount, DEDUCTION_LIMIT_ESI) * ESI_PERCENT) / 100;
  data.net_amount = toNumber(data.gross_amount)-toNumber(data.epf) -toNumber(data.esi) -toNumber(data.advance) +toNumber(employee.bonus) * toNumber(days_worked) +toNumber(employee.allowance) * toNumber(days_worked);

  // Invoice related fields
  data.epf_employer =  employee.epf_uan == "0" ? 0 : (Math.min(data.gross_amount, DEDUCTION_LIMIT) * EPF_EMPLOYER_PERCENT) / 100;
  data.esi_employer = employee.esi_number == "0" ? 0 : (Math.min(data.gross_amount, DEDUCTION_LIMIT_ESI) * ESI_EMPLOYER_PERCENT) / 100;
  data.actual_gross_amount =data.gross_amount + data.epf_employer + data.esi_employer;
  data.service_charge =(data.actual_gross_amount * SERVICE_CHARGE_PERCENT) / 100;
  data.billable_amount = data.actual_gross_amount + data.service_charge;

  return data;
};

const getCombinedInvoiceData = (salaries, includeGst = true) => { 
  const data = { 
    staff_count: 0, 
    duty_count: 0, 
    basic_pay: 0, 
    epf: 0, 
    esi: 0, 
    edli: 0, 
    pf_admin_charges: 0, 
    total_wages: 0, 
    service_charge: 0, 
    total_amount: 0, 
    cgst: 0, 
    sgst: 0, 
  }; 
  if ( 
    salaries[0]?.employee?.client?.contract_category.toLowerCase() !== 
    "manpower outsourcing" 
  ) { 
    const contractValue = 
      Number( 
        salaries[0]?.employee?.client?.contract_value?.replace(/,/g, "") 
      ) || 0; 
    const contractDuration = 
      Number(salaries[0]?.employee?.client?.contract_duration) || 1; 
    const fullContractValue = contractValue / contractDuration; 
 
    // Calculate total wages based on whether to include GST 
    const totalWages = includeGst 
      ? fullContractValue / 1.18 
      : fullContractValue; 
 
    // Only calculate GST if includeGst is true 
    if (includeGst) { 
      data.cgst = (totalWages * 9) / 100; 
      data.sgst = (totalWages * 9) / 100; 
    } 
 
    data.total_wages = totalWages; 
    data.net_receivable = includeGst ? fullContractValue : totalWages; 
 
    data.staff_count = 0; 
    data.duty_count = 0; 
    data.rate = 0; 
    data.basic_pay = 0; 
    data.epf = 0; 
    data.esi = 0; 
    data.edli = 0; 
    data.pf_admin_charges = 0; 
    data.service_charge = 0; 
    data.total_amount = 0; 
 
    salaries.forEach((salary) => { 
      data.staff_count += 1; 
      data.duty_count += salary.days_worked || 0; 
    }); 
 
    return data; 
  } 
 
  let allowance = 0; 
  let bonus = 0; 
  salaries.forEach((salary) => { 
    let remainingAmount = (salary.epf_employer - salary.epf) / 2; 
    allowance = 
      allowance + 
      Number(salary.employee?.allowance) * Number(salary.days_worked); 
    bonus = bonus + Number(salary.employee?.bonus) * Number(salary.days_worked); 
    data.staff_count += 1; 
    data.duty_count += salary.days_worked || 0; 
    data.rate = data.rate || salary.daily_rate; 
    data.basic_pay += salary.gross_amount || 0; 
    data.epf += salary.epf || 0; 
    data.esi = 
      salary.gross_amount <= 21000 
        ? data.esi + salary.esi_employer 
        : data.esi + 0; 
    data.edli += remainingAmount; 
    data.pf_admin_charges += remainingAmount; 
  }); 
 
  data.total_wages = 
    data.basic_pay + 
    data.epf + 
    data.esi + 
    allowance + 
    bonus + 
    data.edli + 
    data.pf_admin_charges; 
  data.service_charge = data.total_wages * 0.0326; 
  data.total_amount = data.service_charge + data.total_wages; 
 
  // Only calculate GST if includeGst is true 
  if (includeGst) { 
    data.cgst = data.total_amount * 0.09; 
    data.sgst = data.total_amount * 0.09; 
    data.net_receivable = data.total_amount + data.cgst + data.sgst; 
  } else { 
    // No GST calculation 
    data.net_receivable = data.total_amount; 
  } 
 
  data.allowance = allowance; 
  data.bonus = bonus; 
 
  return data; 
};

module.exports = { generateSalaryRecordData, getCombinedInvoiceData };